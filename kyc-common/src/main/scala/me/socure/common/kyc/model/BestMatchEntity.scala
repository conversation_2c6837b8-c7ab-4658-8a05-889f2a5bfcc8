package me.socure.common.kyc.model

case class BestMatchEntity(
                            firstName: Option[String] = None,
                            middleName: Option[String] = None,
                            surName: Option[String] = None,
                            suffixName: Option[String] = None,
                            ssn: Option[String] = None,
                            ssnIssued: Option[String] = None,
                            dob: Option[String] = None,
                            mobileNumber: Option[String] = None,
                            emailAddress: Option[String] = None,
                            normalizedAddress: Option[AddressObjectOption] = None,
                            streetAddress: Option[String] = None,
                            city: Option[String] = None,
                            zipCode: Option[String] = None,
                            state: Option[String] = None,
                            ssnDeceased: Option[String] = None,
                            cidDeceased: Option[String] = None,
                            clusterId: Option[String] = None,
                            associatedPhoneNumbers: Option[List[PhoneNumberObject]] = None,
                            associatedAddresses: Option[List[AddressObject]] = None,
                            associatedEmails: Option[List[EmailObject]] = None,
                            matchMetadata: Option[MatchMetadata] = None,
                            deceasedDate: Option[String] = None,
                            age: Option[Int] = None,
                            sourceAttribution: Option[List[String]] = None,
                            socureId: Option[String] = None,
                            derivedSocureId: Option[String] = None,
                            nameAliases: Option[Seq[String]] = None,
                            isValidMatch: Option[Boolean] = None,
                            totalWeight: Option[Double] = None,
                            prefillValidationStatus: Option[Boolean] = None // True if BME can be used to prefill
                          )

case class AddressObject(
                          streetAddress: String,
                          city: String,
                          state: String,
                          zipCode: String,
                          firstSeenDate: Option[String],
                          lastSeenDate: Option[String] = None
                        )

case class AddressObjectOption(
                          streetAddress: Option[String] = None,
                          city: Option[String] = None,
                          state: Option[String] = None,
                          zipCode: Option[String] = None
                        )

case class PhoneNumberObject(
                              phoneNumber: String,
                              firstSeenDate: Option[String],
                              lastSeenDate: Option[String]
                            )

case class EmailObject(
                        emailAddress: String,
                        firstSeenDate: Option[String],
                        lastSeenDate: Option[String]
                      )
