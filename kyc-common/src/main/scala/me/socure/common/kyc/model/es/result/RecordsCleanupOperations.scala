package me.socure.common.kyc.model.es.result

import me.socure.common.kyc.model.DataSources
import me.socure.common.kyc.model.es.result.RecordsCleanupHelper._
import me.socure.common.kyc.util.SSNUtil

import me.socure.common.kyc.model.DataSources
import scala.collection.mutable
import scala.collection.mutable.{Map => MutableMap, Set => MSet}

import scala.util.Try


object MultipleSSNSingleDOB extends RecordsCleanupOperation {
  val name = "MultipleSSNSingleDOB"

  def apply(input: RecordCleanupOperationInput): RecordCleanupOperationResult = {
    val dobYear = input.records.dob.headOption.flatMap(parseYear)
    if (input.records.ssn.length > 1 && input.records.dob.length == 1 && dobYear.isDefined) {

      val filterLogic = (ssnIssuedOpt: Option[String], ssn: Option[String]) => {
        var ssnIssuedVal = ssnIssuedOpt
        if(ssnIssuedOpt.isEmpty || ssnIssuedOpt.exists(ssnIssued => ssnIssued.isEmpty)) {
          ssnIssuedVal = SSNUtil.getSSNIssued(ssn)
        }
        ssnIssuedVal.flatMap(parseYear).forall(ssnYear => (ssnYear + 1) >= dobYear.get) // retain SSNs issued on or after DOB
      }

      val inclusionIndex = input.records.ssn.indices.filter { index =>
        val ssnIssued = getElement(input.records.ssnIssued, index)
        val ssn = getElement(input.records.ssn, index)
        filterLogic(ssnIssued,ssn)
      }.toArray

      var updated = false
      val cleanedRecords = if (input.records.ssn.length == inclusionIndex.length) {
        input.records
      } else {
        updated = true
        filterSSNFields(input.records, inclusionIndex)
      }

      val cleanedIdentityRecords = input.identityRecord.map { identityRecord =>
        val (accepted, removed) = identityRecord.ssn.partition(ssn => filterLogic(ssn.ssnYearLow, Some(ssn.value)))
        identityRecord.copy(
          ssn = accepted,
          removed = identityRecord.removed.copy(ssn = identityRecord.removed.ssn ++ removed)
        )
      }

      RecordCleanupOperationResult(updated, cleanedRecords, cleanedIdentityRecords)
    } else RecordCleanupOperationResult(updated = false, input.records, input.identityRecord)
  }
}

object InvalidStreetAddressCleanup extends RecordsCleanupOperation {

  override val name: String = "InvalidStreetAddressCleanup"

  override def apply(input: RecordCleanupOperationInput): RecordCleanupOperationResult = {
    if (input.records.streetAddress.exists(street => street != null && street.length >= 10 && street.matches("^[0-9 ]+$"))) {
      var (cleanedStreetAddress, updated, indexNeedsUpdate) = filterStreetAddress(input.records.streetAddress)
      val cleanedCity = filterValues(input.records.city, indexNeedsUpdate)
      val cleanedState = filterValues(input.records.state, indexNeedsUpdate)
      val cleanedZipcode = filterValues(input.records.zipCode, indexNeedsUpdate)
      val cleanedARowId = filterValues(input.records.aRowId, indexNeedsUpdate)
      val cleanedAddressType = filterValues(input.records.addressType, indexNeedsUpdate)
      val cleanedAddressFirstSeen = filterValues(input.records.addressFirstSeen, indexNeedsUpdate)
      val cleanedAddressLastSeen = filterValues(input.records.addressLastSeen, indexNeedsUpdate)
      val cleanedIdentityRecords = input.identityRecord.map { identityRecord =>
        if (updated) {
          val (cleanedAddress, identityUpdated, removedFields) = filterAddress(identityRecord.address)
          updated = updated || identityUpdated
          identityRecord.copy(
            removed = identityRecord.removed.copy(address = removedFields), address = cleanedAddress
          )
        }
        else
          identityRecord
      }
      RecordCleanupOperationResult(
        updated,
        input.records.copy(
          streetAddress = cleanedStreetAddress,
          state = cleanedState,
          city = cleanedCity,
          zipCode = cleanedZipcode,
          aRowId = cleanedARowId,
          addressType = cleanedAddressType,
          addressFirstSeen = cleanedAddressFirstSeen,
          addressLastSeen = cleanedAddressLastSeen
        ),
        cleanedIdentityRecords
      )
    } else {
      RecordCleanupOperationResult(updated = false, input.records, input.identityRecord)
    }
  }

  private def filterValues(arr: Array[String], indexSet: Set[Int]): Array[String] = {
    arr.zipWithIndex.filterNot {
      case (_, index) =>
        indexSet.contains(index)
    }.map(_._1)
  }

  private def filterAddress(address: Seq[AddressField]): (Seq[AddressField], Boolean, Seq[AddressField]) = {
    var updated = false
    var removedField = Seq[AddressField]()
    val cleanedAddress = address.filterNot { field =>
      val isFiltered = field.street != null && field.street.length >= 10 && field.street.matches("^[0-9 ]+$")
      updated = isFiltered || updated
      if(isFiltered)
        removedField = removedField ++ Seq(field)
      isFiltered
    }
    (cleanedAddress, updated, removedField)
  }

  private def filterStreetAddress(arr: Array[String]): (Array[String], Boolean, Set[Int]) = {
    var updated = false
    var removedIndex = Set[Int]()
    (arr.zipWithIndex.filterNot {
      case (value, index) => {
        val isFiltered = value != null && value.length >= 10 && value.matches("^[0-9 ]+$")
        updated = isFiltered || updated
        if(isFiltered)
          removedIndex = removedIndex ++ Set(index)
        isFiltered
      }
    }.map(_._1), updated, removedIndex
    )
  }
}

object InvalidSSNCleanup extends RecordsCleanupOperation {
  val name = "InvalidSSNCleanup"

  def apply(input: RecordCleanupOperationInput): RecordCleanupOperationResult = {
    val dobYear = input.records.dob.headOption.flatMap(parseYear)
    if (input.records.ssn.length > 1 && dobYear.exists(_.equals(1962)) && input.records.ssn.exists(_.startsWith("53542"))) {

      val filterLogic = (ssn: String) => {
        val ssnDigits = ssn.replaceAll("\\D", "")
        !ssnDigits.startsWith("53208")
      }

      val (_, inclusionIndex) = input.records.ssn.zipWithIndex.filter {
        case (ssn, _) => filterLogic(ssn)
      }.unzip

      var updated = false
      val cleanedRecords = if (input.records.ssn.length == inclusionIndex.length) {
        input.records
      } else {
        updated = true
        filterSSNFields(input.records, inclusionIndex)
      }

      val cleanedIdentityRecords = input.identityRecord.map { identityRecord =>
        val (accepted, removed) = identityRecord.ssn.partition(ssn => filterLogic(ssn.value))
        identityRecord.copy(
          ssn = accepted,
          removed = identityRecord.removed.copy(ssn = identityRecord.removed.ssn ++ removed)
        )
      }

      RecordCleanupOperationResult(updated, cleanedRecords, cleanedIdentityRecords)
    } else RecordCleanupOperationResult(updated = false, input.records, input.identityRecord)
  }
}

object PrefillSSNBasedOnWeight extends RecordsCleanupOperation {
  val name = "PrefillSSNBasedOnWeight"

  def apply(input: RecordCleanupOperationInput): RecordCleanupOperationResult = {
    var updated = false
    val ssnWeight = input.identityRecord match {
      case Some(identityRecord) =>
      {
        val newSSNWeight: mutable.Map[String, Double] = MutableMap.empty
        identityRecord.allAssociatedSSNs.foreach { ssn =>
          val ssnDigits = ssn.value.replaceAll("\\D", "")
          val isSSNConfirmed = ssn.ssnConfirm.getOrElse("0").equals("1")
          val currentWeight: Double = SSNWeightComputer.computeWeight(SSNWeightComputer(ssnDigits, ssn.source, ssn.isCorrected, isSSNConfirmed))
          if(currentWeight > newSSNWeight.getOrElse(ssnDigits, 0.0)){
            newSSNWeight.put(ssnDigits, currentWeight)
          }
        }
        newSSNWeight.toMap
      }
      case _ => Map[String, Double]()
    }
    val sortedSSN = ssnWeight.toSeq.sortBy(-_._2).map(_._1).filter(ssn=>{
      if((ssn.length == 9 && !SSNWeightComputer.invalidSsnPrefixes.exists(ssn.startsWith))) true
      else false
    })

    val prefilledSSNs = input.records.ssn.map { ssn =>
      val isShortSSN = ssn.length == 4
      val isImproperSSNPrefix = SSNWeightComputer.invalidSsnPrefixes.exists(ssn.startsWith)
      if (isShortSSN || (isImproperSSNPrefix && ssn.length == 9)){
        updated = true
        val last4Digits = ssn.takeRight(4)
        sortedSSN.find(_.endsWith(last4Digits)).getOrElse(ssn)
      }
      else{
        ssn
      }
    }
    val updatedIdentityRecord = if(input.identityRecord.isDefined){
      Some(input.identityRecord.get.copy(ssn = prefilledSSNs.indices.flatMap { idx =>
        Try {
          input.identityRecord.get.ssn(idx).copy(
            value = prefilledSSNs(idx)
          )
        }.toOption
      }))
    }
    else {
      input.identityRecord
    }
    RecordCleanupOperationResult(updated = updated, input.records.copy(ssn = prefilledSSNs), updatedIdentityRecord)
  }
}

class MultipleDOBSingleSSN(skipRemoveAll: Boolean) extends RecordsCleanupOperation {
  val name = "MultipleDOBSingleSSN"

  def apply(input: RecordCleanupOperationInput): RecordCleanupOperationResult = {
    if(input.records.ssn.length == 1) {
      var ssnIssuedYear = input.records.ssnIssued.headOption.flatMap(parseYear)
      val ssn = input.records.ssn.headOption
      if( !ssnIssuedYear.isDefined) ssnIssuedYear = SSNUtil.getSSNIssued(ssn).flatMap(parseYear)

      // Get SSN year range (low and high)
      val ssnYearLow = input.records.ssnYearLow.headOption.flatMap(parseYear)
        .orElse(SSNUtil.ssnLookup(ssn, useV2 = true).map(_.ssnYearLow).flatMap(parseYear))
        .orElse(ssnIssuedYear)

      val ssnYearHigh = input.records.ssnYearHigh.headOption.flatMap(parseYear)
        .orElse(SSNUtil.ssnLookup(ssn, useV2 = true).map(_.ssnYearHigh).flatMap(parseYear))
        .orElse(ssnIssuedYear)

      if ((ssnYearLow.isDefined || ssnYearHigh.isDefined) && input.records.dob.length > 1) {
        val dobsWithYears = input.records.dob.flatMap(dob => parseYear(dob).map(year => (dob, year)))

        if (dobsWithYears.nonEmpty) {
          val selectedDobs = RecordsCleanupHelper.selectDOBsBasedOnSSNRange(dobsWithYears, ssnYearLow, ssnYearHigh)

          val selectedDobStrings = selectedDobs.map(_._1).toSet
          val (cleanedDobs, cleanedRowIds) = input.records.dob.zip(input.records.piiRowIDs.dob).filter {
            case (dob, _) => selectedDobStrings.contains(dob)
          }.unzip

          var updated = false
          val cleanedRecord = if (input.records.dob.length == cleanedDobs.length || (skipRemoveAll && cleanedDobs.isEmpty)) {
            input.records // skip cleanup
          } else {
            updated = true
            input.records.copy(
              dob = cleanedDobs,
              piiRowIDs = input.records.piiRowIDs.copy(dob = cleanedRowIds)
            )
          }

          val cleanedIdentityRecord = input.identityRecord.map { identityRecord =>
            val (accepted, removed) = identityRecord.dob.partition(dob => selectedDobStrings.contains(dob.value))
            if (skipRemoveAll && accepted.isEmpty) {
              identityRecord // skip cleanup
            }
            else {
              identityRecord.copy(
                dob = accepted,
                removed = identityRecord.removed.copy(dob = identityRecord.removed.dob ++ removed)
              )
            }
          }
          RecordCleanupOperationResult(updated, cleanedRecord, cleanedIdentityRecord)
        } else {
          RecordCleanupOperationResult(updated = false, input.records, input.identityRecord)
        }
      } else RecordCleanupOperationResult(updated = false, input.records, input.identityRecord)

    } else RecordCleanupOperationResult(updated = false, input.records, input.identityRecord)
  }
}

object MultipleDOBMultipleSSN extends RecordsCleanupOperation {
  val name = "MultipleDOBMultipleSSN"
  def apply(input: RecordCleanupOperationInput): RecordCleanupOperationResult = {
    if(input.records.ssn.length > 1 && input.records.dob.length > 1) {

      // Filter SSNs based on minimum DOB year
      val minDobYrOnFile = input.records.dob.flatMap(parseYear).toList match {
        case Nil => None
        case xs => Some(xs.min)
      }

      val (filteredRecords, ssnUpdated) = if (minDobYrOnFile.isDefined) {
        val filterSSN = (ssnIssuedOpt: Option[String], ssn: Option[String]) => {
          val ssnIssuedVal = ssnIssuedOpt.filter(_.nonEmpty).orElse(SSNUtil.getSSNIssued(ssn))
          ssnIssuedVal.flatMap(parseYear).forall(ssnYear => ssnYear + 1 >= minDobYrOnFile.get)
          // retain SSNs issued on or after min (DOB on file) - 1
        }
        val inclusionIndex = input.records.ssn.indices.filter { index =>
          val ssnIssued = getElement(input.records.ssnIssued, index)
          val ssn = getElement(input.records.ssn, index)
          filterSSN(ssnIssued,ssn)
        }.toArray

        val cleanedRecords = if (input.records.ssn.length == inclusionIndex.length) {
          input.records
        } else {
          filterSSNFields(input.records, inclusionIndex)
        }

        val updated = !(input.records.ssn.length == inclusionIndex.length)
        (cleanedRecords, updated)
      } else {
        (input.records, false)
      }

     // Filter DOBs based on SSN issuance year range
      if (filteredRecords.dob.length > 1) {
        // Get SSN year ranges for all remaining SSNs
        val ssnYearRanges = filteredRecords.ssn.indices.map { index =>
          val ssn = getElement(filteredRecords.ssn, index)
          val ssnYearLow = getElement(filteredRecords.ssnYearLow, index).flatMap(parseYear)
            .orElse(SSNUtil.ssnLookup(ssn, useV2 = true).map(_.ssnYearLow).flatMap(parseYear))
            .orElse(getElement(filteredRecords.ssnIssued, index).flatMap(parseYear))
            .orElse(SSNUtil.getSSNIssued(ssn).flatMap(parseYear))

          val ssnYearHigh = getElement(filteredRecords.ssnYearHigh, index).flatMap(parseYear)
            .orElse(SSNUtil.ssnLookup(ssn, useV2 = true).map(_.ssnYearHigh).flatMap(parseYear))
            .orElse(getElement(filteredRecords.ssnIssued, index).flatMap(parseYear))
            .orElse(SSNUtil.getSSNIssued(ssn).flatMap(parseYear))

          (ssnYearLow, ssnYearHigh)
        }.filter { case (low, high) => low.isDefined || high.isDefined }

        if (ssnYearRanges.nonEmpty) {
          // Find the overall range: lowest SSN year to highest SSN year
          val allLowYears = ssnYearRanges.flatMap(_._1)
          val allHighYears = ssnYearRanges.flatMap(_._2)

          val overallLowYear = if (allLowYears.nonEmpty) Some(allLowYears.min) else None
          val overallHighYear = if (allHighYears.nonEmpty) Some(allHighYears.max) else None

          if (overallLowYear.isDefined || overallHighYear.isDefined) {
            val dobsWithYears = filteredRecords.dob.flatMap(dob => parseYear(dob).map(year => (dob, year)))

            if (dobsWithYears.nonEmpty) {
              val selectedDobs = RecordsCleanupHelper.selectDOBsBasedOnSSNRange(dobsWithYears, overallLowYear, overallHighYear)

              val selectedDobStrings = selectedDobs.map(_._1).toSet
              val (cleanedDobs, cleanedRowIds) = filteredRecords.dob.zip(filteredRecords.piiRowIDs.dob).filter {
                case (dob, _) => selectedDobStrings.contains(dob)
              }.unzip

              val dobUpdated = filteredRecords.dob.length != cleanedDobs.length
              val finalRecord = if (dobUpdated) {
                filteredRecords.copy(
                  dob = cleanedDobs,
                  piiRowIDs = filteredRecords.piiRowIDs.copy(dob = cleanedRowIds)
                )
              } else {
                filteredRecords
              }

              val cleanedIdentityRecord = input.identityRecord.map { identityRecord =>
                // Apply both SSN and DOB filtering to identity record
                val ssnFiltered = if (minDobYrOnFile.isDefined) {
                  val filterSSN = (ssnIssuedOpt: Option[String], ssn: Option[String]) => {
                    val ssnIssuedVal = ssnIssuedOpt.filter(_.nonEmpty).orElse(SSNUtil.getSSNIssued(ssn))
                    ssnIssuedVal.flatMap(parseYear).forall(ssnYear => ssnYear + 1 >= minDobYrOnFile.get)
                  }
                  val (acceptedSSN, removedSSN) = identityRecord.ssn.partition(ssn => filterSSN(ssn.ssnYearLow, Some(ssn.value)))
                  (acceptedSSN, removedSSN)
                } else {
                  (identityRecord.ssn, Seq.empty)
                }

                val (acceptedDOB, removedDOB) = identityRecord.dob.partition(dob => selectedDobStrings.contains(dob.value))

                identityRecord.copy(
                  ssn = ssnFiltered._1,
                  dob = acceptedDOB,
                  removed = identityRecord.removed.copy(
                    ssn = identityRecord.removed.ssn ++ ssnFiltered._2,
                    dob = identityRecord.removed.dob ++ removedDOB
                  )
                )
              }

              RecordCleanupOperationResult(ssnUpdated || dobUpdated, finalRecord, cleanedIdentityRecord)
            } else {
              RecordCleanupOperationResult(ssnUpdated, filteredRecords, input.identityRecord)
            }
          } else {
            RecordCleanupOperationResult(ssnUpdated, filteredRecords, input.identityRecord)
          }
        } else {
          RecordCleanupOperationResult(ssnUpdated, filteredRecords, input.identityRecord)
        }
      } else {
        RecordCleanupOperationResult(ssnUpdated, filteredRecords, input.identityRecord)
      }
    } else RecordCleanupOperationResult(updated = false, input.records, input.identityRecord)
  }
}

object RemoveNonCHMSSN extends RecordsCleanupOperation {
  val name = "RemoveNonCHMSSN"

  def apply(input: RecordCleanupOperationInput): RecordCleanupOperationResult = {
    val containsCHMSSN = input.records.ciRowId.exists(_.contains("CHM"))
    if (input.records.ssn.length > 1 && containsCHMSSN) {
      val inputSSN = input.request.nationalId.map(_.value)
      val input4DigitSSN = inputSSN.map(_.takeRight(4))
      val isNineDigitSSN = inputSSN.exists(value => value.nonEmpty && value.length == 9 && value.slice(0, 5) != "00000")

      val filterLogic = (rowId: String, ssnOnRecord: String) => {
        lazy val ssnMatch = inputSSN.exists(_.equals(ssnOnRecord))
        lazy val ssn4DigitMatch = input4DigitSSN.exists(_.equals(ssnOnRecord.takeRight(4)))
        if (ssnMatch || (!isNineDigitSSN && ssn4DigitMatch)) true // retain if matched with input
        else rowId.contains("CHM")
      }

      val (_, inclusionIndex) = input.records.ciRowId.zipWithIndex.filter { case (rowId, index) => filterLogic(rowId, input.records.ssn(index)) }.unzip

      var updated = false
      val cleanedRecords = if (input.records.ssn.length == inclusionIndex.length) {
        input.records
      } else {
        updated = true
        filterSSNFields(input.records, inclusionIndex)
      }

      val cleanedIdentityRecords = input.identityRecord.map { identityRecord =>
        val (accepted, removed) = identityRecord.ssn.partition(ssn => filterLogic(ssn.rowId.getOrElse(""), ssn.value))
        identityRecord.copy(
          ssn = accepted,
          removed = identityRecord.removed.copy(ssn = identityRecord.removed.ssn ++ removed)
        )
      }

      RecordCleanupOperationResult(updated, cleanedRecords, cleanedIdentityRecords)
    } else RecordCleanupOperationResult(updated = false, input.records, input.identityRecord)
  }
}

object RecordsCleanupHelper {

  def filterSSNFields(records: Records, inclusionIndex: Array[Int]): Records = {
    val inclusionIndexSet = inclusionIndex.toSet
    records.copy(
      ssn = filterElements(records.ssn, inclusionIndexSet),
      ssnConfirm = filterElements(records.ssnConfirm, inclusionIndexSet),
      invalidSSN = filterElements(records.invalidSSN, inclusionIndexSet),
      ssnYearHigh = filterElements(records.ssnYearHigh, inclusionIndexSet),
      ssnYearLow = filterElements(records.ssnYearLow, inclusionIndexSet),
      ssnIssued = filterElements(records.ssnIssued, inclusionIndexSet),
      cidDeceased = filterElements(records.cidDeceased, inclusionIndexSet),
      ssnDeceased = filterElements(records.ssnDeceased, inclusionIndexSet),
      factaCode = filterElements(records.factaCode, inclusionIndexSet),
      deceasedDate = filterElements(records.deceasedDate, inclusionIndexSet),
      ciRowId = filterElements(records.ciRowId, inclusionIndexSet)
    )
  }

  private def filterElements(arr: Array[String], inclusionIndexSet: Set[Int]): Array[String] = {
    arr.zipWithIndex.filter {
      case (_, index) => inclusionIndexSet.contains(index)
    }.map(_._1)
  }

    def parseYear(dateStr: String): Option[Int] = {
    def isValidYear(year: Int): Boolean = year >= 1800 && year <= 2100

    Try {
      val dateStrPadded = dateStr.replaceAll("\\D", "").reverse.padTo(8, '0').reverse
      val year1 = dateStrPadded.substring(0, 4).toInt
      if (isValidYear(year1)) Some(year1)
      else {
        val year2 = dateStrPadded.substring(4, 8).toInt
        if (isValidYear(year2)) Some(year2)
        else None
      }
    }.toOption.flatten
  }

  def getElement(arr: Array[String], index: Int): Option[String] = if (index < arr.length) Some(arr(index)) else None

  def selectDOBsBasedOnSSNRange(dobsWithYears: Array[(String, Int)], ssnYearLow: Option[Int], ssnYearHigh: Option[Int]): Array[(String, Int)] = {
    val lowYear = ssnYearLow.getOrElse(Int.MinValue)
    val highYear = ssnYearHigh.getOrElse(Int.MaxValue)

    // Consider DOBs within SSN issuance year range + 1 year
    val dobsInRange = dobsWithYears.filter { case (_, dobYear) =>
      dobYear >= lowYear && dobYear <= (highYear + 1)
    }

    if (dobsInRange.nonEmpty) {
      dobsInRange
    } else {
      // If all DOBs are prior to SSN issuance year, select the DOB closest to the issuance year
      val allDobsPriorToIssuance = dobsWithYears.forall { case (_, dobYear) => dobYear < lowYear }

      if (allDobsPriorToIssuance) {
        // If all DOBs are prior to SSN issuance year, select the DOB closest to the SSN range
        val targetYear = if (lowYear != Int.MinValue) lowYear else highYear
        val closestDob = dobsWithYears.minBy { case (_, dobYear) => math.abs(dobYear - targetYear) }
        Array(closestDob)
      } else {
        // If range is same, consider DOBs closest to the SSN issued date
        val targetYear = if (lowYear == highYear) lowYear else (lowYear + highYear) / 2
        val closestDob = dobsWithYears.minBy { case (_, dobYear) => math.abs(dobYear - targetYear) }
        Array(closestDob)
      }
    }
  }
}

object RequestBasedNameCleanup extends RecordsCleanupOperation {

  override val name: String = "FN/SN Correction based on I/P"

  override def apply(input: RecordCleanupOperationInput): RecordCleanupOperationResult = {

    val ipNameMap = Map(
        input.request.firstName.value.replaceAll("\\s", "") -> input.request.firstName.value,
        input.request.surName.value.replaceAll("\\s", "")-> input.request.surName.value
    )

    val cleanedRecords = input.records.copy(
      firstName = transformName(ipNameMap, input.records.firstName),
      surName = transformName(ipNameMap, input.records.surName)
    )

    val cleanedIdentityRecords = input.identityRecord.map(iR => iR.copy(
      firstName = transformNameRecords(ipNameMap, iR.firstName),
      surName = transformNameRecords(ipNameMap, iR.surName)
    ))
    RecordCleanupOperationResult(false, cleanedRecords, cleanedIdentityRecords)
  }
  private def transformName(ipNameMap: Map[String, String], nameLst: Array[String]) = {
    nameLst.map { x =>
      ipNameMap.getOrElse(x, x)
    }
  }
  private def transformNameRecords(ipNameMap: Map[String, String], fields: Seq[PIIField]) = {
    fields.map { x =>
      x.copy(value = ipNameMap.getOrElse(x.value, x.value))
    }
  }
}


object PlaceHolderNamesCleanUp extends RecordsCleanupOperation {
  private def toTitleCase(word: String): String = {
    if (word.isEmpty) {
      ""
    } else {
      word.substring(0, 1).toUpperCase + word.substring(1).toLowerCase
    }
  }

  override val name: String = "PlaceHolderNamesCleanUp"

  override def apply(input: RecordCleanupOperationInput): RecordCleanupOperationResult = {
    var inputNeedsCleaning: Boolean = false
    var updated = false
    val fullName: Array[String] = (input.records.firstName zip input.records.surName).map {
      case (firstName, lastName) =>
        val fullName = s"${toTitleCase(firstName)} ${toTitleCase(lastName)}"
        inputNeedsCleaning = inputNeedsCleaning || (fullName.equals("Jane Doe") || fullName.equals("John Doe"))
        fullName
    }
    if(inputNeedsCleaning){
      val inclusionIndex = fullName.zipWithIndex.filter{
        case (name, idx) => !(name.equals("Jane Doe") || name.equals("John Doe"))
      }.map {
        case (name, idx) => idx
      }

      if(!(input.records.firstName.length == inclusionIndex.length)){
        val cleanedRecords = if (input.records.firstName.length == inclusionIndex.length || inclusionIndex.length==0) {
          input.records
        } else {
          input.records.copy(
            firstName = inclusionIndex.map(input.records.firstName),
            surName = inclusionIndex.map(input.records.surName),
            piiRowIDs = input.records.piiRowIDs.copy(
              firstName = inclusionIndex.map(input.records.piiRowIDs.firstName),
              surName = inclusionIndex.map(input.records.piiRowIDs.surName)
            )
          )
        }

        val cleanedIdentityRecords = input.identityRecord.map { identityRecord =>
          val (acceptedFN, removedFN) = identityRecord.firstName.zipWithIndex.partition{
            case(_, idx) => inclusionIndex.contains(idx)
          }
          val (acceptedSN, removedSN) = identityRecord.surName.zipWithIndex.partition{
            case(_, idx) => inclusionIndex.contains(idx)
          }
          identityRecord.copy(
            firstName = acceptedFN.map(_._1),
            surName = acceptedSN.map(_._1),
            removed = identityRecord.removed.append(IdentityRecordRemoved(firstName = removedFN.map(_._1))).append(IdentityRecordRemoved(surName = removedSN.map(_._1)))
          )
        }
        RecordCleanupOperationResult(updated = true, cleanedRecords, cleanedIdentityRecords)
      }
      else
        RecordCleanupOperationResult(updated = false, input.records, input.identityRecord)
    }
    else
      RecordCleanupOperationResult(updated = false, input.records, input.identityRecord)
  }
}

object InitialFirstNameCleanUp extends RecordsCleanupOperation {
  override val name: String = "InitialFirstNameCleanUp"

  private def filterLogicEnhanced(prefixSet:Set[Char], fn: String) = {
    if (fn.length == 1) {
      !prefixSet.contains(fn.charAt(0))
    }
    else {
      fn.length > 1
    }
  }
  override def apply(input: RecordCleanupOperationInput): RecordCleanupOperationResult = {

    val singleCharFirstNameExists = input.records.firstName.exists(_.length == 1)
    var updated: Boolean = false

    if(singleCharFirstNameExists){
      val prefixSet = input.records.firstName.filter(_.length > 1).map(_.charAt(0).toUpper).toSet
      val inclusionIndex = input.records.firstName.zipWithIndex.collect {
        case (fn, idx) if filterLogicEnhanced(prefixSet, fn.toUpperCase) => idx
      }
      updated = !(input.records.firstName.length == inclusionIndex.length)
      if(updated){
        val cleanedRecords = input.records.copy(
            firstName = inclusionIndex.map(input.records.firstName),
            piiRowIDs = input.records.piiRowIDs.copy(
              firstName = inclusionIndex.map(input.records.piiRowIDs.firstName)
            )
        )
        val cleanedIdentityRecords = input.identityRecord.map { identityRecord =>
          val (accepted, removed) = identityRecord.firstName.partition {
            case (fn) => filterLogicEnhanced(prefixSet, fn.value.toUpperCase)
          }
          identityRecord.copy(
            firstName = accepted,
            removed = identityRecord.removed.append(IdentityRecordRemoved(firstName = removed))
          )
        }
        RecordCleanupOperationResult(updated, cleanedRecords, cleanedIdentityRecords)
      }
      else
        RecordCleanupOperationResult(updated = false, input.records, input.identityRecord)
    }
    else
      RecordCleanupOperationResult(updated = false, input.records, input.identityRecord)
  }
}
