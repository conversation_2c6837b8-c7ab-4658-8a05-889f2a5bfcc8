package me.socure.common.kyc.model

import me.socure.common.kyc.model.FeatureFlags.FeatureFlag

case class KYCSearchRequestResolved(
                                     originalReq: KycEntitySearchRequest,
                                     resolvedReq: KycEntitySearchRequest,
                                     nationalIdResolved: Option[NationalIdResolved],
                                     processingMetadata: KycProcessingMetadata = KycProcessingMetadata(
                                       inputPiiCount = 3,
                                       isNotMoreThan2Pii = false,
                                       is4DigitSsn = false
                                     ),
                                     featureFlags: Map[FeatureFlags.FeatureFlag, Boolean] = Map.empty
                                   ) {

  def setAdditionalNameMatchingCheck(additionalNameMatchingCheck: Boolean): KYCSearchRequestResolved = {
    val customPreferences = this.resolvedReq.customPreferencesKyc.copy(additionalNameMatchingCheck = Some(additionalNameMatchingCheck))
    val resolvedReq = this.resolvedReq.copy(customPreferencesKyc = customPreferences)
    this.copy(resolvedReq = resolvedReq)
  }

  def setFeatureFlags(featureFlags: Map[FeatureFlag, Boolean]): KYCSearchRequestResolved = {
    this.copy(featureFlags = featureFlags)
  }
}
