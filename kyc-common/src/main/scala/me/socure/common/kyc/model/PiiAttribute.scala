package me.socure.common.kyc.model

object PiiAttribute extends Enumeration
{
  type PiiAttribute = EnumVal
  sealed class EnumVal(val id : Int,val name : String,searchTag: String) extends Value

   val FirstNameMatch     =  new EnumVal(0, "firstName", "NF")
   val SurNameMatch       =  new EnumVal(1, "surName","NL")
   val SSNMatch           =  new EnumVal(2, "ssn",searchTag = "CI")
   val DOBMatch           =  new EnumVal(3, "dob","BD")
   val MobileNumberMatch  =  new EnumVal(4, "mobileNumber","PN")
   val StreetAddressMatch =  new EnumVal(5, "streetAddress","AX")
   val CityMatch          =  new EnumVal(6, "city","AY")
   val ZipCodeMatch       =  new EnumVal(7, "zipCode","AZ")
   val StateMatch         =  new EnumVal(8, "state","AS")
   val EmailMatch         =  new EnumVal(9, "email", "EM")
   val SSN4Match           =  new EnumVal(10, "ssn4",searchTag = "CILAST4")

   val valueList = Seq(
    FirstNameMatch,
    SurNameMatch,
    SSNMatch,
    DOBMatch,
    MobileNumberMatch,
    EmailMatch,
    StreetAddressMatch,
    CityMatch,
    ZipCodeMatch,
    StateMatch,
    SSN4Match
   )
}
