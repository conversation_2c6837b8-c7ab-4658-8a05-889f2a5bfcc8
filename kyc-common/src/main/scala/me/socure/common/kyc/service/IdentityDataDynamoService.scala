package me.socure.common.kyc.service

import com.typesafe.config.Config
import me.socure.common.data.core.provider._
import me.socure.common.kyc.CommonUtil
import me.socure.common.kyc.model.RawIdentityResult
import me.socure.common.kyc.util.FutureUtils
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.retry.strategy.RetryStrategy
import org.json4s.jackson.JsonMethods
import org.slf4j.{Logger, LoggerFactory}
import software.amazon.awssdk.services.dynamodb.DynamoDbClient
import software.amazon.awssdk.services.dynamodb.model.{AttributeValue, BatchGetItemRequest, KeysAndAttributes, ReturnConsumedCapacity}

import scala.collection.JavaConverters._
import scala.concurrent.duration.{Duration, FiniteDuration}
import scala.concurrent.{ExecutionContext, Future, TimeoutException}
import scala.util.Try

class IdentityDataDynamoService(
                                 tableName: String,
                                 dynamoDbClient: DynamoDbClient,
                                 retryStrategy: RetryStrategy = RetryStrategy.Never,
                                 batchFetchSize: Int = 25,
                                 batchFetchTimeout: Option[FiniteDuration] = None
                               )(implicit ec: ExecutionContext) {

  private val metrics: Metrics = JavaMetricsFactory.get("dynamodb.identity")
  private val logger: Logger = LoggerFactory.getLogger(getClass)

  def fetchSourceDataAsync(clusterIds: Array[String]): Future[Array[RawIdentityResult]] = {

    val futures = clusterIds.grouped(batchFetchSize).map(fetchSourceDataAsyncHelper).toSeq
    Future.sequence(futures).map(_.flatten.toArray)
  }

  private def fetchSourceDataAsyncHelper(clusterIds: Array[String]): Future[Array[RawIdentityResult]] = {
    def createFutureFunction(withTimeout: Boolean): () => Future[Array[RawIdentityResult]] = () => {
      val sourceDataFuture = Future {
        fetchSourceData(clusterIds)
      }
      if (withTimeout && batchFetchTimeout.nonEmpty) {
        FutureUtils.getFutureWithTimeout(sourceDataFuture, batchFetchTimeout.get).map {
          case Right(result) => result
          case Left(timeoutError) => throw new TimeoutException(timeoutError)
        }
      } else sourceDataFuture
    }

    val futureWithRetry = new RichDataProvider[Array[RawIdentityResult]](createFutureFunction(true)).retry(retryStrategy).apply()

    futureWithRetry.recoverWith {
      case ex: TimeoutException =>
        logger.error(s"Table=$tableName BatchGet call timed out even after multiple retries", ex)
        metrics.increment("batch.get.timeout", s"table:$tableName")
        metrics.timeFuture("batch.get.last.attempt", s"table:$tableName")(createFutureFunction(false)()) // return the future without timeout
      case ex: Throwable => Future.failed(ex)
    }
  }

  def fetchSourceData(clusterIds: Array[String]): Array[RawIdentityResult] = {
    if (clusterIds.isEmpty) return Array.empty

    def fetchResults(clusterIdBatch: Array[String]): Seq[RawIdentityResult] = {
      val keysToGet = clusterIdBatch.map { clusterId =>
        Map("clusterId" -> AttributeValue.builder().s(clusterId).build()).asJava
      }.toSeq

      val request = BatchGetItemRequest.builder()
        .requestItems(Map(tableName -> KeysAndAttributes.builder().keys(keysToGet.asJava).build()).asJava)
        .returnConsumedCapacity(ReturnConsumedCapacity.TOTAL)
        .build()

      dynamoDbClient.batchGetItemPaginator(request).asScala.flatMap { response =>
        val consumedCapacityUnits = response.consumedCapacity().asScala.map(_.capacityUnits().toDouble).sum
        metrics.count("rcu.count", consumedCapacityUnits.toLong, s"table:$tableName")
        response.responses().get(tableName).asScala.map(IdentityDataDynamoService.processIdentityRecordItem)
      }.toSeq
    }

    val keysBatch = clusterIds.grouped(batchFetchSize).toSeq
    val results = keysBatch.flatMap(fetchResults)

    if (results.length != clusterIds.length) {
      metrics.increment("records.size.mismatch", s"table:$tableName")
      val notFoundClusterIds = clusterIds.toSet.diff(results.map(_.clusterId).toSet)
      logger.error(s"Following cluster ids not found in dynamoDB table $tableName - ${notFoundClusterIds.mkString(",")}")
    }

    results.toArray
  }
}

object IdentityDataDynamoService {
  def processIdentityRecordItem(item: java.util.Map[String, AttributeValue]): RawIdentityResult = {
    val clusterId = item.get("clusterId").s()
    val attributesBytes = item.get("attributes").b().asByteArray()
    val metaData = if (item.containsKey("metaData")) Try(JsonMethods.parse(item.get("metaData").s())).toOption else None
    val attributesJson = CommonUtil.gZipDecompress(attributesBytes)
    RawIdentityResult(clusterId, JsonMethods.parse(attributesJson), metaData)
  }

  def apply(config: Config, dynamoDbClient: DynamoDbClient)(implicit ec: ExecutionContext): IdentityDataDynamoService = {
    val retryStrategy = if (config.hasPath("dynamo.retry.count")) RetryStrategy.times(config.getInt("dynamo.retry.count")) else RetryStrategy.Never
    val batchFetchSize = if (config.hasPath("dynamo.batch.fetch.size")) config.getInt("dynamo.batch.fetch.size") else 25
    val batchFetchTimeout = if (config.hasPath("dynamo.batch.fetch.timeout")) Some(Duration(config.getString("dynamo.batch.fetch.timeout")).asInstanceOf[FiniteDuration]) else None
    new IdentityDataDynamoService(
      tableName = config.getString("dynamo.table"),
      dynamoDbClient = dynamoDbClient,
      retryStrategy = retryStrategy,
      batchFetchSize = batchFetchSize,
      batchFetchTimeout = batchFetchTimeout
    )
  }
}