package me.socure.common.kyc.model

import me.socure.types.scala.{ByMember, Enum}

object FeatureFlags extends Enum with ByMember {

  type FeatureFlag = EnumVal
  val EnableSSNIssuedDateNewFormat: FeatureFlag = new FeatureFlag(1, "IdPlusFeatures", "EnableSSNIssuedDateNewFormat")
  val EnablePrefillBMEValidation: FeatureFlag = new FeatureFlag(2, "KycFeatures", "EnablePrefillBMEValidation")

  def listFlags: Seq[FeatureFlag] = {
    values
  }

  sealed case class EnumVal(id: Int, group: String, name: String) extends Value
}

