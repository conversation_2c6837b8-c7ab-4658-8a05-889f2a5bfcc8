package me.socure.common.kyc.service

import me.socure.common.kyc.model.RawIdentityResult
import me.socure.common.retry.strategy.RetryStrategy
import org.json4s.jackson.JsonMethods
import org.mockito.Matchers.any
import org.mockito.Mockito._
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{FreeSpec, Matchers}
import software.amazon.awssdk.core.SdkBytes
import software.amazon.awssdk.services.dynamodb.DynamoDbClient
import software.amazon.awssdk.services.dynamodb.model.{AttributeValue, BatchGetItemRequest, BatchGetItemResponse}
import software.amazon.awssdk.services.dynamodb.paginators.BatchGetItemIterable

import java.util.Base64
import scala.collection.JavaConverters._
import scala.concurrent.duration._
import scala.concurrent.{Await, ExecutionContext}

class IdentityDataDynamoServiceTest extends FreeSpec with Matchers with MockitoSugar with ScalaFutures {

  val tableName = "kyc_identity_data_test"
  val client: DynamoDbClient = mock[DynamoDbClient]
  implicit val ec: ExecutionContext = ExecutionContext.Implicits.global
  implicit override val patienceConfig: PatienceConfig = PatienceConfig(timeout = 100 seconds, interval = 500 milliseconds)

  val identityDataDynamoService = new IdentityDataDynamoService(tableName, client)

  "should fetch source data from dynamoDB" in {
    val compressedAttributes = base64Decode("H4sIAAAAAAAAA8xXbW/bNhD+K4I+xwVfxDd/y5wF7VanQxwkXYdCYCR6ViuJASnHy4L895GWpUiRvLgY3O6LLd3xTnfPc+QdH8OL83D6x2N4L/O1Cqfh1UoX0oYnYZIrWar0eievGrmR5ddwCt0Cna+L0qlKWah4mRlbOfUyy/3y2du5X6s371L3hiie/vwxdsKYRYRiQDEkQnDMBcOQTigjbnUqK2darvP8JCxUJc9kJcPpY2jXt19UUsWpSpS0yjsE4dPTyXPQM51n5TDmZCc+LOT3592QBQA+ZCeMKRQM4AgjEDEOKOAMgwkFVLQxhwhAMQFwgsgV5FMMpgB8CrtZ+KyePp+Epz2wIUXB4ib4VVaZCmbyzv3oSnnRolJvAkqQwMGiyKpVpTdlcH0aRJwwPszUO7Kb4OvWUeIdJd6RE1n3V/uxrZ972foZQiPT1Chr42RtjCqPRGmSVQ/OSZuaW2qrGsrrU/fyd3bnHpsgs9QmsowTXRTKJJnMtxVwMl4Y3pNRqjoQ3vDJkfLxMAid79Pf3VLbCft04QT30j99akPu1ialMLipSQze6jzXm2Dm4vvqw7jUuggoZjCYnwWUE8KG1Hr7TU1esKrtk629i8y09kXa2teUohFKl9o4AOFRKe0wOT9rmWxie42xg8BqKTsImoa0hqsibbiqg+pytdBr5+pSPhS6TIdU2K3atOoaabwXaXTczfMi2Ab2FvQD8G6hbEv7RYpbyBrAelj9spal+3rqtmMwl6U2NrhRtgquslIGHy4DLiIeDTH84s1MbVbUZhtvVnkzbVqz/QeTVeY+S9QzthezLrYcU4+tE8YcYiEQhwhTATHjFGE4wXVNHHpwP+7Aist1catMjcaOgTbfFtJY3klTFf7c3G2FrbR6uNtuiG2+Hao+XLZkNXk3X3MNqg/ysy7NjOM002WXwCGuDaUtvA2d2jSE1h/1fWmxuOh1JsTFhOCJYBAOOXRKgneqIU/WlkeaA6zvAaVr28WujrPSxZs1Vf2gpIlX2Z+rGvt3AqJ/bxRbA3dq1Ot3XebFlOGwuVj0oJmbN0NICrN/zrDr5TL7a++gwWEzaEDoyhERzqEvWx5h9+YGDYaG9QrYK4PGb30+acQYIVRQMNJhurphDoW2VTMNxHcrXaoj0fvqmCcwIYJySkfaZE83bIFWucpJ4/9PLiQiCLEIYDTMpaf7NkKOP8R2cmAOGcAIhXSYQ0/3bTkc9Tx/mYPAmGMgkBjpVT3df+EhQlHDg6CEQFeqgELsq4px6HnoVJPPgU/czsfwlT1+Me/t8TN5n41MLelOvOd0KrI0zX/YNpitpMnVyH0zaRUHxt1HHML2VEXAAY4IwBRGgFMIAeMecTBSNdEriP901r+/CcKAI3fkQvasGcaf6tu5GxO+K+a+XN73gr/UVo5Cb1rFHuhz+eOu+ufajNT4spYOB/L6ol9P498/7s9P/wAAAP//AwA7qzFcYBEAAA==")
    val dynamoRequest = BatchGetItemRequest.builder().build()
    val dynamoResponse = BatchGetItemResponse.builder().responses(
      Map(tableName -> List(
        Map(
          "clusterId" -> AttributeValue.builder().s("id1234").build(),
          "attributes" -> AttributeValue.builder().b(SdkBytes.fromByteArray(compressedAttributes)).build()
        ).asJava
      ).asJava
      ).asJava
    ).build()
    when(client.batchGetItem(any[BatchGetItemRequest]())).thenReturn(dynamoResponse)
    when(client.batchGetItemPaginator(any[BatchGetItemRequest]())).thenReturn(new BatchGetItemIterable(client, dynamoRequest))

    val results = identityDataDynamoService.fetchSourceData(Array("id1234"))
    results(0) shouldBe RawIdentityResult("id1234", JsonMethods.parse("{\"NF\":[{\"value\":\"Thomas\",\"cleanedValue\":\"thomas\",\"rank\":1,\"column\":\"name_first\",\"file\":\"CHM\",\"rowId\":\"263:EX_CHM_74563063159983897316-675\",\"date\":null,\"metaData\":{\"subject_deceased\":\"0\"}},{\"value\":\"Colin\",\"cleanedValue\":\"colin\",\"rank\":1,\"column\":\"name_first\",\"file\":\"LFM\",\"rowId\":\"900:EX_LFM_61970343204780608730-6069\",\"date\":\"2019-01-25T18:30:00Z\",\"metaData\":null}],\"A\":[{\"value\":\"162 SW Katie Cape Cote SW Ste. 65293 Smithtown VA 48578\",\"cleanedValue\":\"162 sw katie cape cote sw ste 65293 smithtown va 48578\",\"rank\":1,\"column\":\"address_current\",\"file\":\"CHM\",\"rowId\":\"263:EX_CHM_74563063159983897316-675\",\"date\":null,\"metaData\":{\"city\":\"Smithtown\",\"state\":\"VA\",\"zip\":\"48578\",\"idscan_commercial\":\"0\",\"subject_deceased\":\"0\",\"street\":\"162 SW Katie Cape Cote SW Ste. 65293\"},\"AX\":\"162 sw katie cape cote sw ste 65293\",\"AY\":\"smithtown\",\"AS\":\"va\",\"AZ\":\"48578\"},{\"value\":\"661 W Smith Hollow Creek SW Room 6371 MD 68557\",\"cleanedValue\":\"661 w smith hollow creek sw room 6371 md 68557\",\"rank\":2,\"column\":\"address_former1\",\"file\":\"CHM\",\"rowId\":\"263:EX_CHM_74563063159983897316-675\",\"date\":null,\"metaData\":{\"city\":\"\",\"state\":\"MD\",\"zip\":\"68557\",\"subject_deceased\":\"0\",\"street\":\"661 W Smith Hollow Creek SW Room 6371\"},\"AX\":\"661 w smith hollow creek sw room 6371\",\"AY\":\"\",\"AS\":\"md\",\"AZ\":\"68557\"},{\"value\":\"South Raymond\",\"cleanedValue\":\"south raymond\",\"rank\":3,\"column\":\"address_former2\",\"file\":\"CHM\",\"rowId\":\"263:EX_CHM_74563063159983897316-675\",\"date\":null,\"metaData\":{\"city\":\"South Raymond\",\"state\":\"\",\"zip\":\"\",\"subject_deceased\":\"0\",\"street\":\"\"},\"AX\":\"\",\"AY\":\"south raymond\",\"AS\":\"\",\"AZ\":\"\"},{\"value\":\"Juan Radial Manors West Tina OR 89484\",\"cleanedValue\":\"juan radial manors west tina or 89484\",\"rank\":1,\"column\":\"address_service\",\"file\":\"NCM\",\"rowId\":\"836:EX_NCM_81399281236913786231-3371\",\"date\":\"2019-01-25T18:30:00Z\",\"metaData\":{\"street_number\":\"\",\"city\":\"West Tina\",\"street_apartment\":\"\",\"street_type\":\"Manors\",\"state\":\"OR\",\"zip\":\"89484\",\"street_name\":\"Juan Radial\",\"street_direction\":\"\"},\"AX\":\"juan radial manors\",\"AY\":\"west tina\",\"AS\":\"or\",\"AZ\":\"89484\"}],\"SSN\":[{\"value\":\"***********\",\"cleanedValue\":\"289539711\",\"rank\":1,\"column\":\"ssn\",\"file\":\"CHM\",\"rowId\":\"263:EX_CHM_74563063159983897316-675\",\"date\":null,\"metaData\":{\"ssn_confirm\":\"0\",\"invalid\":\"0\",\"year_high\":\"\",\"I912\":\"0\",\"subject_deceased\":\"0\",\"year_low\":\"\",\"idscan_deceased\":\"0\"}}],\"NS\":[{\"value\":\"Mr.\",\"cleanedValue\":\"mr\",\"rank\":1,\"column\":\"name_suffix\",\"file\":\"LFM\",\"rowId\":\"981:EX_LFM_11201258819928843012-6072\",\"date\":\"2019-01-07T18:30:00Z\",\"metaData\":null}],\"PN\":[{\"value\":\"6477556960\",\"cleanedValue\":\"6477556960\",\"rank\":1,\"column\":\"most_current_phone\",\"file\":\"CHM\",\"rowId\":\"263:EX_CHM_74563063159983897316-675\",\"date\":null,\"metaData\":{\"subject_deceased\":\"0\"}},{\"value\":\"9355968667\",\"cleanedValue\":\"9355968667\",\"rank\":2,\"column\":\"second_most_current_phone\",\"file\":\"CHM\",\"rowId\":\"263:EX_CHM_74563063159983897316-675\",\"date\":null,\"metaData\":{\"subject_deceased\":\"0\"}},{\"value\":\"5452274032\",\"cleanedValue\":\"5452274032\",\"rank\":1,\"column\":\"most_current_phone\",\"file\":\"LFM\",\"rowId\":\"900:EX_LFM_61970343204780608730-6069\",\"date\":\"2019-01-25T18:30:00Z\",\"metaData\":null},{\"value\":\"7745075616\",\"cleanedValue\":\"7745075616\",\"rank\":1,\"column\":\"most_current_phone\",\"file\":\"NCM\",\"rowId\":\"836:EX_NCM_81399281236913786231-3371\",\"date\":\"2019-01-25T18:30:00Z\",\"metaData\":null},{\"value\":\"9338309294\",\"cleanedValue\":\"9338309294\",\"rank\":1,\"column\":\"most_current_phone\",\"file\":\"LFM\",\"rowId\":\"424:EX_LFM_96551559061363067810-6075\",\"date\":\"2018-12-31T18:30:00Z\",\"metaData\":null}],\"NM\":[{\"value\":\"David\",\"cleanedValue\":\"david\",\"rank\":1,\"column\":\"name_middle\",\"file\":\"CHM\",\"rowId\":\"263:EX_CHM_74563063159983897316-675\",\"date\":null,\"metaData\":{\"subject_deceased\":\"0\"}},{\"value\":\"Charles\",\"cleanedValue\":\"charles\",\"rank\":1,\"column\":\"name_middle\",\"file\":\"LFM\",\"rowId\":\"111:EX_LFM_20515250361408611078-6070\",\"date\":\"2019-01-24T18:30:00Z\",\"metaData\":null}],\"BD\":[{\"value\":\"19570424\",\"cleanedValue\":\"19570424\",\"rank\":1,\"column\":\"dobMart\",\"file\":\"CHM\",\"rowId\":\"263:EX_CHM_74563063159983897316-675\",\"date\":null,\"metaData\":{\"subject_deceased\":\"0\"}}],\"NL\":[{\"value\":\"Rosales\",\"cleanedValue\":\"rosales\",\"rank\":1,\"column\":\"name_last\",\"file\":\"CHM\",\"rowId\":\"263:EX_CHM_74563063159983897316-675\",\"date\":null,\"metaData\":{\"subject_deceased\":\"0\"}},{\"value\":\"Ford\",\"cleanedValue\":\"ford\",\"rank\":3,\"column\":\"name_former2_last\",\"file\":\"CHM\",\"rowId\":\"263:EX_CHM_74563063159983897316-675\",\"date\":null,\"metaData\":{\"subject_deceased\":\"0\"}}]}"))
  }

  "should fetch source data asynchronously from dynamoDB" in {
    val compressedAttributes = base64Decode("H4sIAAAAAAAAA8xXbW/bNhD+K4I+xwVfxDd/y5wF7VanQxwkXYdCYCR6ViuJASnHy4L895GWpUiRvLgY3O6LLd3xTnfPc+QdH8OL83D6x2N4L/O1Cqfh1UoX0oYnYZIrWar0eievGrmR5ddwCt0Cna+L0qlKWah4mRlbOfUyy/3y2du5X6s371L3hiie/vwxdsKYRYRiQDEkQnDMBcOQTigjbnUqK2darvP8JCxUJc9kJcPpY2jXt19UUsWpSpS0yjsE4dPTyXPQM51n5TDmZCc+LOT3592QBQA+ZCeMKRQM4AgjEDEOKOAMgwkFVLQxhwhAMQFwgsgV5FMMpgB8CrtZ+KyePp+Epz2wIUXB4ib4VVaZCmbyzv3oSnnRolJvAkqQwMGiyKpVpTdlcH0aRJwwPszUO7Kb4OvWUeIdJd6RE1n3V/uxrZ972foZQiPT1Chr42RtjCqPRGmSVQ/OSZuaW2qrGsrrU/fyd3bnHpsgs9QmsowTXRTKJJnMtxVwMl4Y3pNRqjoQ3vDJkfLxMAid79Pf3VLbCft04QT30j99akPu1ialMLipSQze6jzXm2Dm4vvqw7jUuggoZjCYnwWUE8KG1Hr7TU1esKrtk629i8y09kXa2teUohFKl9o4AOFRKe0wOT9rmWxie42xg8BqKTsImoa0hqsibbiqg+pytdBr5+pSPhS6TIdU2K3atOoaabwXaXTczfMi2Ab2FvQD8G6hbEv7RYpbyBrAelj9spal+3rqtmMwl6U2NrhRtgquslIGHy4DLiIeDTH84s1MbVbUZhtvVnkzbVqz/QeTVeY+S9QzthezLrYcU4+tE8YcYiEQhwhTATHjFGE4wXVNHHpwP+7Aist1catMjcaOgTbfFtJY3klTFf7c3G2FrbR6uNtuiG2+Hao+XLZkNXk3X3MNqg/ysy7NjOM002WXwCGuDaUtvA2d2jSE1h/1fWmxuOh1JsTFhOCJYBAOOXRKgneqIU/WlkeaA6zvAaVr28WujrPSxZs1Vf2gpIlX2Z+rGvt3AqJ/bxRbA3dq1Ot3XebFlOGwuVj0oJmbN0NICrN/zrDr5TL7a++gwWEzaEDoyhERzqEvWx5h9+YGDYaG9QrYK4PGb30+acQYIVRQMNJhurphDoW2VTMNxHcrXaoj0fvqmCcwIYJySkfaZE83bIFWucpJ4/9PLiQiCLEIYDTMpaf7NkKOP8R2cmAOGcAIhXSYQ0/3bTkc9Tx/mYPAmGMgkBjpVT3df+EhQlHDg6CEQFeqgELsq4px6HnoVJPPgU/czsfwlT1+Me/t8TN5n41MLelOvOd0KrI0zX/YNpitpMnVyH0zaRUHxt1HHML2VEXAAY4IwBRGgFMIAeMecTBSNdEriP901r+/CcKAI3fkQvasGcaf6tu5GxO+K+a+XN73gr/UVo5Cb1rFHuhz+eOu+ufajNT4spYOB/L6ol9P498/7s9P/wAAAP//AwA7qzFcYBEAAA==") // same value as sync test
    val dynamoRequest = BatchGetItemRequest.builder().build()
    val dynamoResponse = BatchGetItemResponse.builder().responses(
      Map(tableName -> List(
        Map(
          "clusterId" -> AttributeValue.builder().s("id1234").build(),
          "attributes" -> AttributeValue.builder().b(SdkBytes.fromByteArray(compressedAttributes)).build()
        ).asJava
      ).asJava
      ).asJava
    ).build()
    when(client.batchGetItem(any[BatchGetItemRequest]())).thenReturn(dynamoResponse)
    when(client.batchGetItemPaginator(any[BatchGetItemRequest]())).thenReturn(new BatchGetItemIterable(client, dynamoRequest))

    val futureResult = identityDataDynamoService.fetchSourceDataAsync(Array("id1234"))

    val results = Await.result(futureResult, 5.seconds)
    results(0) shouldBe RawIdentityResult("id1234", JsonMethods.parse("{\"NF\":[{\"value\":\"Thomas\",\"cleanedValue\":\"thomas\",\"rank\":1,\"column\":\"name_first\",\"file\":\"CHM\",\"rowId\":\"263:EX_CHM_74563063159983897316-675\",\"date\":null,\"metaData\":{\"subject_deceased\":\"0\"}},{\"value\":\"Colin\",\"cleanedValue\":\"colin\",\"rank\":1,\"column\":\"name_first\",\"file\":\"LFM\",\"rowId\":\"900:EX_LFM_61970343204780608730-6069\",\"date\":\"2019-01-25T18:30:00Z\",\"metaData\":null}],\"A\":[{\"value\":\"162 SW Katie Cape Cote SW Ste. 65293 Smithtown VA 48578\",\"cleanedValue\":\"162 sw katie cape cote sw ste 65293 smithtown va 48578\",\"rank\":1,\"column\":\"address_current\",\"file\":\"CHM\",\"rowId\":\"263:EX_CHM_74563063159983897316-675\",\"date\":null,\"metaData\":{\"city\":\"Smithtown\",\"state\":\"VA\",\"zip\":\"48578\",\"idscan_commercial\":\"0\",\"subject_deceased\":\"0\",\"street\":\"162 SW Katie Cape Cote SW Ste. 65293\"},\"AX\":\"162 sw katie cape cote sw ste 65293\",\"AY\":\"smithtown\",\"AS\":\"va\",\"AZ\":\"48578\"},{\"value\":\"661 W Smith Hollow Creek SW Room 6371 MD 68557\",\"cleanedValue\":\"661 w smith hollow creek sw room 6371 md 68557\",\"rank\":2,\"column\":\"address_former1\",\"file\":\"CHM\",\"rowId\":\"263:EX_CHM_74563063159983897316-675\",\"date\":null,\"metaData\":{\"city\":\"\",\"state\":\"MD\",\"zip\":\"68557\",\"subject_deceased\":\"0\",\"street\":\"661 W Smith Hollow Creek SW Room 6371\"},\"AX\":\"661 w smith hollow creek sw room 6371\",\"AY\":\"\",\"AS\":\"md\",\"AZ\":\"68557\"},{\"value\":\"South Raymond\",\"cleanedValue\":\"south raymond\",\"rank\":3,\"column\":\"address_former2\",\"file\":\"CHM\",\"rowId\":\"263:EX_CHM_74563063159983897316-675\",\"date\":null,\"metaData\":{\"city\":\"South Raymond\",\"state\":\"\",\"zip\":\"\",\"subject_deceased\":\"0\",\"street\":\"\"},\"AX\":\"\",\"AY\":\"south raymond\",\"AS\":\"\",\"AZ\":\"\"},{\"value\":\"Juan Radial Manors West Tina OR 89484\",\"cleanedValue\":\"juan radial manors west tina or 89484\",\"rank\":1,\"column\":\"address_service\",\"file\":\"NCM\",\"rowId\":\"836:EX_NCM_81399281236913786231-3371\",\"date\":\"2019-01-25T18:30:00Z\",\"metaData\":{\"street_number\":\"\",\"city\":\"West Tina\",\"street_apartment\":\"\",\"street_type\":\"Manors\",\"state\":\"OR\",\"zip\":\"89484\",\"street_name\":\"Juan Radial\",\"street_direction\":\"\"},\"AX\":\"juan radial manors\",\"AY\":\"west tina\",\"AS\":\"or\",\"AZ\":\"89484\"}],\"SSN\":[{\"value\":\"***********\",\"cleanedValue\":\"289539711\",\"rank\":1,\"column\":\"ssn\",\"file\":\"CHM\",\"rowId\":\"263:EX_CHM_74563063159983897316-675\",\"date\":null,\"metaData\":{\"ssn_confirm\":\"0\",\"invalid\":\"0\",\"year_high\":\"\",\"I912\":\"0\",\"subject_deceased\":\"0\",\"year_low\":\"\",\"idscan_deceased\":\"0\"}}],\"NS\":[{\"value\":\"Mr.\",\"cleanedValue\":\"mr\",\"rank\":1,\"column\":\"name_suffix\",\"file\":\"LFM\",\"rowId\":\"981:EX_LFM_11201258819928843012-6072\",\"date\":\"2019-01-07T18:30:00Z\",\"metaData\":null}],\"PN\":[{\"value\":\"6477556960\",\"cleanedValue\":\"6477556960\",\"rank\":1,\"column\":\"most_current_phone\",\"file\":\"CHM\",\"rowId\":\"263:EX_CHM_74563063159983897316-675\",\"date\":null,\"metaData\":{\"subject_deceased\":\"0\"}},{\"value\":\"9355968667\",\"cleanedValue\":\"9355968667\",\"rank\":2,\"column\":\"second_most_current_phone\",\"file\":\"CHM\",\"rowId\":\"263:EX_CHM_74563063159983897316-675\",\"date\":null,\"metaData\":{\"subject_deceased\":\"0\"}},{\"value\":\"5452274032\",\"cleanedValue\":\"5452274032\",\"rank\":1,\"column\":\"most_current_phone\",\"file\":\"LFM\",\"rowId\":\"900:EX_LFM_61970343204780608730-6069\",\"date\":\"2019-01-25T18:30:00Z\",\"metaData\":null},{\"value\":\"7745075616\",\"cleanedValue\":\"7745075616\",\"rank\":1,\"column\":\"most_current_phone\",\"file\":\"NCM\",\"rowId\":\"836:EX_NCM_81399281236913786231-3371\",\"date\":\"2019-01-25T18:30:00Z\",\"metaData\":null},{\"value\":\"9338309294\",\"cleanedValue\":\"9338309294\",\"rank\":1,\"column\":\"most_current_phone\",\"file\":\"LFM\",\"rowId\":\"424:EX_LFM_96551559061363067810-6075\",\"date\":\"2018-12-31T18:30:00Z\",\"metaData\":null}],\"NM\":[{\"value\":\"David\",\"cleanedValue\":\"david\",\"rank\":1,\"column\":\"name_middle\",\"file\":\"CHM\",\"rowId\":\"263:EX_CHM_74563063159983897316-675\",\"date\":null,\"metaData\":{\"subject_deceased\":\"0\"}},{\"value\":\"Charles\",\"cleanedValue\":\"charles\",\"rank\":1,\"column\":\"name_middle\",\"file\":\"LFM\",\"rowId\":\"111:EX_LFM_20515250361408611078-6070\",\"date\":\"2019-01-24T18:30:00Z\",\"metaData\":null}],\"BD\":[{\"value\":\"19570424\",\"cleanedValue\":\"19570424\",\"rank\":1,\"column\":\"dobMart\",\"file\":\"CHM\",\"rowId\":\"263:EX_CHM_74563063159983897316-675\",\"date\":null,\"metaData\":{\"subject_deceased\":\"0\"}}],\"NL\":[{\"value\":\"Rosales\",\"cleanedValue\":\"rosales\",\"rank\":1,\"column\":\"name_last\",\"file\":\"CHM\",\"rowId\":\"263:EX_CHM_74563063159983897316-675\",\"date\":null,\"metaData\":{\"subject_deceased\":\"0\"}},{\"value\":\"Ford\",\"cleanedValue\":\"ford\",\"rank\":3,\"column\":\"name_former2_last\",\"file\":\"CHM\",\"rowId\":\"263:EX_CHM_74563063159983897316-675\",\"date\":null,\"metaData\":{\"subject_deceased\":\"0\"}}]}"))
  }

  "should retry and recover after timeout in fetchSourceDataAsync" in {
    val retryStrategy = RetryStrategy.times(2)

    val service = new TestIdentityDataDynamoService(
      tableName,
      client,
      retryStrategy,
      batchFetchTimeout = Some(FiniteDuration(10, "millis")) // intentionally low
    )

    val resultFuture = service.fetchSourceDataAsync(Array("id-timeout"))

    whenReady(resultFuture) { result =>
      result should have size 1
      result.head.clusterId shouldBe "id-timeout"
      //service.callCount shouldBe 2
    }
  }

  "should log and return fallback on total timeout in fetchSourceDataAsync" in {
    val retryStrategy = RetryStrategy.times(2)

    val service = new AlwaysTimeoutIdentityDataDynamoService(
      tableName,
      client,
      retryStrategy,
      batchFetchTimeout = Some(FiniteDuration(10, "millis"))
    )

    val resultFuture = service.fetchSourceDataAsync(Array("id-fallback"))

    whenReady(resultFuture) { result =>
      result should have size 1
      result.head.clusterId shouldBe "id-fallback"
      //service.callCount shouldBe 4
    }
  }

  def base64Decode(encodedStr: String): Array[Byte] = {
    Base64.getDecoder.decode(encodedStr)
  }
}

class TestIdentityDataDynamoService(
                                     tableName: String,
                                     dynamoDbClient: DynamoDbClient,
                                     retryStrategy: RetryStrategy,
                                     batchFetchTimeout: Option[FiniteDuration]
                                   )(implicit ec: ExecutionContext)
  extends IdentityDataDynamoService(tableName, dynamoDbClient, retryStrategy, 25, batchFetchTimeout) {

  var callCount = 0

  override def fetchSourceData(clusterIds: Array[String]): Array[RawIdentityResult] = {
    callCount += 1
    // Simulate slow response on first attempt, fast on retry
    if (callCount == 1) {
      Thread.sleep(batchFetchTimeout.map(_.toMillis + 500).getOrElse(1000L))
    }
    Array(RawIdentityResult(clusterIds.head, JsonMethods.parse("{}")))
  }
}


class AlwaysTimeoutIdentityDataDynamoService(
                                              tableName: String,
                                              dynamoDbClient: DynamoDbClient,
                                              retryStrategy: RetryStrategy,
                                              batchFetchTimeout: Option[FiniteDuration]
                                            )(implicit ec: ExecutionContext)
  extends IdentityDataDynamoService(tableName, dynamoDbClient, retryStrategy, 25, batchFetchTimeout) {

  var callCount = 0

  override def fetchSourceData(clusterIds: Array[String]): Array[RawIdentityResult] = {
    callCount += 1
    Thread.sleep(batchFetchTimeout.map(_.toMillis + 500).getOrElse(1000L))
    Array(RawIdentityResult(clusterIds.head, JsonMethods.parse("{}")))
  }
}