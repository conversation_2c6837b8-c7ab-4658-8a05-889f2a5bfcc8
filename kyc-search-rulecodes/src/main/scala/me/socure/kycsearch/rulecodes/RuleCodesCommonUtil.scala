package me.socure.kycsearch.rulecodes

import com.socure.domain.socure.scoring.RuleCodes.{EX_CITY_SCORE, EX_CONFIDENCE_SCORE, EX_DOB_MISKEYED, EX_DOB_SCORE, EX_DOB_YYYY_MATCH, EX_EMAIL_SCORE, EX_FIRSTNAME_SCORE, EX_FIRST_NAME_POSSIBLY_MISTYPED, EX_LASTNAME_SCORE, EX_NAME_POSSIBLY_MISTYPED, EX_PHONE_SCORE, EX_SSN_MISKEYED, EX_SSN_SCORE, EX_STATE_SCORE, EX_STREETADDRESS_SCORE, EX_ZIP_SCORE}
import me.socure.common.kyc.model.KYCSearchRequestResolved

object RuleCodesCommonUtil {

  def generateConfidenceScore(ruleCodes: Map[String, Double], searchRequest: KYCSearchRequestResolved): Map[String, Double] = {
    def getScore(code: String): Double = ruleCodes.getOrElse(code, 0.0)
    def isMatched(code: String): Boolean = getScore(code) > 0.01

    if (searchRequest.nationalIdResolved.isEmpty ||
      searchRequest.nationalIdResolved.get.original.value.isEmpty ||
      searchRequest.resolvedReq.firstName.value.isEmpty ||
      searchRequest.resolvedReq.surName.value.isEmpty ||
      searchRequest.resolvedReq.dob.isEmpty ||
      searchRequest.resolvedReq.dob.get.value.isEmpty) {
      Map.empty
    } else {

      val baseScores = Seq(
        if (isMatched(EX_FIRSTNAME_SCORE.code.toString)) 10.0 else -5.0,
        if (isMatched(EX_LASTNAME_SCORE.code.toString)) 10.0 else -5.0,
        if (isMatched(EX_PHONE_SCORE.code.toString)) 3.0 else 0.0,
        if (isMatched(EX_DOB_SCORE.code.toString) || isMatched(EX_DOB_YYYY_MATCH.code.toString)) 10.0 else -5.0,
        if (isMatched(EX_EMAIL_SCORE.code.toString)) 3.0 else 0.0,
        if (
          isMatched(EX_STREETADDRESS_SCORE.code.toString) &&
            (
              (isMatched(EX_CITY_SCORE.code.toString) && isMatched(EX_STATE_SCORE.code.toString)) ||
                isMatched(EX_ZIP_SCORE.code.toString)
              )
        ) 3.0 else 0.0,
        // SSN Score
        if (isMatched(EX_SSN_SCORE.code.toString)) {
          if (searchRequest.processingMetadata.is4DigitSsn) 40.0 else 60.0
        } else {
          if (searchRequest.processingMetadata.is4DigitSsn) 40.0 else 60.0
        },
        // Fuzzy scores
        if (isMatched(EX_FIRST_NAME_POSSIBLY_MISTYPED.code.toString)) -2.5 else 0.0,
        if (isMatched(EX_NAME_POSSIBLY_MISTYPED.code.toString)) -2.5 else 0.0,
        if (isMatched(EX_DOB_MISKEYED.code.toString)) -2.5 else 0.0,
        if (isMatched(EX_SSN_MISKEYED.code.toString) || isMatched(EX_DOB_YYYY_MATCH.code.toString)) -7.5 else 0.0
      )

      val additionalScores = Seq(
        70.0,
        if(searchRequest.processingMetadata.is4DigitSsn) 0.0 else 20.0,
        if (searchRequest.resolvedReq.mobileNumber.exists(_.value.nonEmpty)) 3.0 else 0.0,
        if (searchRequest.resolvedReq.email.exists(_.nonEmpty)) 3.0 else 0.0,
        {
          val city = searchRequest.resolvedReq.city.map(_.value).getOrElse("")
          val state = searchRequest.resolvedReq.state.map(_.value).getOrElse("")
          val zip = searchRequest.resolvedReq.zipCode.map(_.value).getOrElse("")
          val street = searchRequest.resolvedReq.streetAddress.map(_.value).getOrElse("")
          if (street.nonEmpty && ((city.nonEmpty && state.nonEmpty) || zip.nonEmpty)) 4.0 else 0.0
        }
      )

      val totalScore = (baseScores.sum) / (additionalScores.sum)
      Map(EX_CONFIDENCE_SCORE.code.toString -> totalScore)
    }
  }

}
