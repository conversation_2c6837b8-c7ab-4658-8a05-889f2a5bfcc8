package me.socure.kycsearch.matcher

import me.socure.common.kyc.model.PiiAttribute._
import me.socure.common.kyc.model.es.result.Records
import me.socure.common.kyc.model.{AddressComponents, CleanedStreetAddress, DOB, DobMatcherUtil, KYCPreferences, KYCSearchRequestResolved, KycEntitySearchRequest, KycProcessingMetadata, MiddleName, MobileNumber, NationalId, StreetAddress, SurName, ZipCode}
import me.socure.kycsearch.matcher.AddressMatchers._
import me.socure.kycsearch.matcher.DOBMatchers._
import me.socure.kycsearch.matcher.NameMatchers._
import me.socure.kycsearch.matcher.NationalIDMacthers.{matchNationalId, matchNationalIdFuzzy, matchNationalIdPartial}
import me.socure.kycsearch.matcher.PhoneNumberMatchers.matchPN
import me.socure.service.constants.DobMatchLogic._

import scala.collection.concurrent.TrieMap

object ResultMatcherHelper {

  private val DOBOneYearRadius = 1

  def getPiiMatchResults(
                          searchRequestResolved: KYCSearchRequestResolved,
                          record: Records,
                          useExactMatch: Boolean,
                          entity: Boolean,
                          bestMatchedElementTracker: TrieMap[String, String]
                        ): Map[PiiAttribute, Int] = {
    val searchRequest = searchRequestResolved.resolvedReq
    val preferences = if (useExactMatch || searchRequest.isSinglePiiRequest) {
      KYCPreferences.exactMatch
    } else {
      if (entity) searchRequest.preferencesEntity else searchRequest.preferencesKyc
    }
    val kycPreferences = if (searchRequest.customPreferencesKyc.customDobMatchLogic == None && !useExactMatch) preferences.dobMatchLogic else searchRequest.customPreferencesKyc.customDobMatchLogic
    val dobMatchLogic = DobMatcherUtil.getDobMatchingLogic(preferences.exactDob, kycPreferences, searchRequestResolved.processingMetadata.isNotMoreThan2Pii)
    val customNameMatchLogic = if (searchRequest.customPreferencesKyc.customNameMatching != None && !useExactMatch) searchRequest.customPreferencesKyc.customNameMatching.get else ""
    val additionalNameMatchingCheck: Boolean = searchRequest.customPreferencesKyc.additionalNameMatchingCheck.getOrElse(false)
    val isSSNExact = preferences.exactSSN
    val originalId = searchRequestResolved.nationalIdResolved.map(_.original).getOrElse(NationalId(""))
    val cleanedStreetAddress = cleanStreetAddress(searchRequest.streetAddress)
    getPiiMatchResultsHelper(searchRequestResolved, record, bestMatchedElementTracker, searchRequest, preferences, dobMatchLogic, customNameMatchLogic, additionalNameMatchingCheck, isSSNExact, originalId, cleanedStreetAddress)
  }

  def getPiiMatchResultsHelper(searchRequestResolved: KYCSearchRequestResolved,
                               record: Records,
                               bestMatchedElementTracker: TrieMap[String, String],
                               searchRequest: KycEntitySearchRequest,
                               preferences: KYCPreferences,
                               dobMatchLogic: DobMatchLogic,
                               customNameMatchLogic: String,
                               additionalNameMatchingCheck: Boolean,
                               isSSNExact: Boolean,
                               originalId: NationalId,
                               cleanedStreetAddress: Option[CleanedStreetAddress]
                              ) = {
    val cashappNamematching = customNameMatchLogic match {
      case "custom_name_matching_1001" =>
        calculateKycCustom1001NameMatch(searchRequest.firstName.value, searchRequest.surName.value, record.firstName, record.middleName, record.surName, record.suffixName, additionalNameMatchingCheck, bestMatchedElementTracker)
      case _ => ""
    }
    val piiMatch = Map(
      if (cashappNamematching == "N" || cashappNamematching == "NF") FirstNameMatch -> 1 else if (customNameMatchLogic == "" && calculateKyc(searchRequest.firstName.value, record.firstName, record.middleName, record.surName, "NF", exactMatch = true, originalId = originalId, bestMatchedElementTracker)) FirstNameMatch -> 1 else FirstNameMatch -> 0,
      if (cashappNamematching == "N" || cashappNamematching == "NL") SurNameMatch -> 1 else if (customNameMatchLogic == "" && calculateKyc(searchRequest.surName.value, record.surName, "NL", exactMatch = true, originalId = originalId, bestMatchedElementTracker)) SurNameMatch -> 1 else SurNameMatch -> 0,
      if (calculateKyc(searchRequest.nationalId.map(_.value).getOrElse(""), record.ssn, "CI", exactMatch = isSSNExact, originalId = originalId, bestMatchedElementTracker, Some(searchRequestResolved.processingMetadata), preferences.nationalIdMatchLogic)) SSNMatch -> 1 else SSNMatch -> 0,
      if (calculateKycForBD(searchRequest.dob.map(_.value).getOrElse(""), record.dob, dobMatchLogic = dobMatchLogic, bestMatchedElementTracker)) DOBMatch -> 1 else DOBMatch -> 0,
      if (calculateKyc(searchRequest.mobileNumber.map(_.value).getOrElse(""), record.mobileNumber, "PN", exactMatch = true, originalId = originalId, bestMatchedElementTracker)) MobileNumberMatch -> 1 else MobileNumberMatch -> 0,
      if (calculateKyc(searchRequest.email.getOrElse(""), record.emailAddress, "EM", exactMatch = true, originalId = originalId, bestMatchedElementTracker)) EmailMatch -> 1 else EmailMatch -> 0,
      if (calculateKyc(cleanedStreetAddress.map(_.value).getOrElse(""), record.streetAddress, "AX", exactMatch = true, originalId = originalId, bestMatchedElementTracker, addressComponents = searchRequest.addressComponents, addressMatchLogic = preferences.addressMatchLogic)) StreetAddressMatch -> 1 else StreetAddressMatch -> 0,
      if (calculateKyc(searchRequest.city.map(_.value).getOrElse(""), record.city, "AY", exactMatch = true, originalId = originalId, bestMatchedElementTracker)) CityMatch -> 1 else CityMatch -> 0,
      if (calculateKyc(searchRequest.zipCode.map(_.value).getOrElse(""), record.zipCode, "AZ", exactMatch = true, originalId = originalId, bestMatchedElementTracker)) ZipCodeMatch -> 1 else ZipCodeMatch -> 0,
      if (calculateKyc(searchRequest.state.map(_.value).getOrElse(""), record.state, "AS", exactMatch = true, originalId = originalId, bestMatchedElementTracker)) StateMatch -> 1 else StateMatch -> 0
    )
    if(piiMatch.getOrElse(SSNMatch, 0) == 0){
      piiMatch ++ Map(
        if (calculateKyc(searchRequest.nationalId.map(_.value).getOrElse(""), record.ssn, "CILAST4", exactMatch = isSSNExact, originalId = originalId, bestMatchedElementTracker, Some(searchRequestResolved.processingMetadata), preferences.nationalIdMatchLogic)) SSN4Match -> 1 else SSN4Match -> 0
      )
    }
    else{
      piiMatch
    }
  }

  // whenever you add new match logic here kindly add the new logic in DOBResolverHelper.findClosestMatchedDOB()
  private def calculateKycForBD(testValue: String, vendorValues: Array[String], dobMatchLogic: DobMatchLogic, bestMatchedElement: scala.collection.concurrent.TrieMap[String, String], exactMatch: Boolean = false): Boolean = {
    val computedDobMatchLogic: DobMatchLogic = if (exactMatch) EXACT_MATCH else dobMatchLogic
    vendorValues.indexWhere(x => {
      computedDobMatchLogic match {
        case EXACT_MATCH => matchBD(DOB(testValue), DOB(x))
        case FUZZY_MATCH => checkYearRadius(DOB(testValue), DOB(x), true, DOBOneYearRadius)
        case EXACT_YYYY_MATCH => matchDobYearOnly(DOB(testValue), DOB(x))
        case FUZZY_YYYY_MATCH => matchDobYearOnly(DOB(testValue), DOB(x), Some(true), DOBOneYearRadius)
        case EXACT_YYYY_MM_MATCH => matchDobYearMonthOnly(DOB(testValue), DOB(x))
        case FUZZY_YYYY_MM_MATCH => matchDobYearMonthOnly(DOB(testValue), DOB(x), isFuzzy = true, DOBOneYearRadius)
        case FUZZY_TWO_DIGIT_TRANSPOSITION_YYYY_MM_MATCH => matchDobFuzzy2DigitTransposition(DOB(testValue), DOB(x), isDay = false, DOBOneYearRadius)
        case FUZZY_TWO_DIGIT_TRANSPOSITION_YYYY_MM_DD_MATCH => matchDobFuzzy2DigitTransposition(DOB(testValue), DOB(x), isDay = true, DOBOneYearRadius)
        case TWO_DIGIT_TRANSPOSITION_YYYY_MATCH => matchDob2DigitTransposition(DOB(testValue), DOB(x))
        case CUSTOM_DOB_LOGIC_1001 => matchBDCustom1001(DOB(testValue), DOB(x))
        case EXACT_YYYY_SWAPPED_MM_DD_MATCH => matchDobYearOnlyMonthDaySwap(DOB(testValue), DOB(x))
        case _ => false
      }
    }) match {
      case -1 => {
        if (vendorValues.nonEmpty) bestMatchedElement("dob") = vendorValues.head
        else bestMatchedElement("dob") = ""
        false
      }
      case index => {
        bestMatchedElement("dob") = vendorValues(index)
        true
      }
    }
  }

  private def calculateKycCustom1001NameMatch(inputFirstName: String, inputSurName: String, fnVendorValues: Array[String], mnVendorValues: Array[String], lnVendorValues: Array[String], nsVendorValues: Array[String], isAdditionalCriteriaCheckEnabled: Boolean, bestMatchedElement: scala.collection.concurrent.TrieMap[String, String] = scala.collection.concurrent.TrieMap[String, String]()): String = {
    customMatchN1001(inputFirstName, inputSurName, fnVendorValues, mnVendorValues, lnVendorValues, nsVendorValues, isAdditionalCriteriaCheckEnabled, bestMatchedElement)
  }

  /**
   * Method used for improving the first name score based on middle name match.
   * testValue - input data from request
   * Please retain the argument order while invoking on any other fields, which will ensure order of comparing the search results
   * fnVendorValues - First Name Array from Vendor Result
   * mnVendorValues - Middle Name Array from Vendor Result
   * lnVendorValues - Last Name Array from Vendor Result
   */
  private def calculateKyc(testValue: String, fnVendorValues: Array[String], mnVendorValues: Array[String], lnVendorValues: Array[String], piiTag: String, exactMatch: Boolean, originalId: NationalId, bestMatchedElement: scala.collection.concurrent.TrieMap[String, String]): Boolean = {
    piiTag match {
      case "NF" => {
        if (fnVendorValues.nonEmpty && mnVendorValues.nonEmpty) {
          val maxLen = Seq(fnVendorValues.length, mnVendorValues.length).max
          fnVendorValues.padTo(maxLen, "").zip(mnVendorValues.padTo(maxLen, "")).exists {
            case (firstName: String, middleName: String) => {
              fuzzyInputNameMatchWithMultipleVendorFields(testValue, firstName, middleName) match {
                case true => {
                  bestMatchedElement("firstName") = firstName
                  bestMatchedElement("middleName") = middleName
                  true
                }
                case false => {
                  bestMatchedElement("firstName") = fnVendorValues.head
                  bestMatchedElement("middleName") = mnVendorValues.head
                  false
                }
              }
            }
          }
        }
        else if (fnVendorValues.nonEmpty && mnVendorValues.isEmpty) {
          fnVendorValues.indexWhere(fuzzyInputNameMatchWithMultipleVendorFields(testValue, _, "")) match {
            case -1 => {
              bestMatchedElement("firstName") = fnVendorValues.head
              false
            }
            case index => {
              bestMatchedElement("firstName") = fnVendorValues(index)
              true
            }
          }
        }
        else if (mnVendorValues.nonEmpty && fnVendorValues.isEmpty) {
          mnVendorValues.indexWhere(fuzzyInputNameMatchWithMultipleVendorFields(testValue, "", _)) match {
            case -1 => {
              bestMatchedElement("middleName") = mnVendorValues.head
              false
            }
            case index => {
              bestMatchedElement("middleName") = mnVendorValues(index)
              true
            }
          }
        }
        else {
          false
        }
      }
      case _ => false
    }
  }

  /**
   * In case of 2PII request, The PII elements SSN, DOB, PN, EM must be exact matched.
   */
  private def calculateKyc(
                            testValue: String,
                            vendorValues: Array[String],
                            piiTag: String,
                            exactMatch: Boolean,
                            originalId: NationalId,
                            bestMatchedElement: scala.collection.concurrent.TrieMap[String, String] = scala.collection.concurrent.TrieMap[String, String](),
                            processingMetadata: Option[KycProcessingMetadata] = None,
                            nationalIdMatchLogicOpt: Option[String] = None,
                            addressComponents: Option[AddressComponents] = None,
                            addressMatchLogic: Option[String] = None,
                            isRetry: Boolean = false
                          ): Boolean = {
    piiTag match {
      case "NF" => vendorValues.exists(fuzzyNameMatchWithSynonymCheck(testValue, _))
      case "NL" => {
        vendorValues.indexWhere(x => fuzzyNameMatchWithSynonymCheckL(SurName(testValue), SurName(x))) match {
          case -1 => {
            if (vendorValues.nonEmpty) bestMatchedElement("surName") = vendorValues.head
            else bestMatchedElement("surName") = ""
            false
          }
          case index => {
            bestMatchedElement("surName") = vendorValues(index)
            true
          }
        }
      }
      case "NM" => (testValue.isEmpty match {
        case true => false
        case false => vendorValues.exists(x => fuzzyMiddleNameMatchWithSynonymCheck(MiddleName(testValue), MiddleName(x)))
      })
      case "CILAST4" => {
        vendorValues.indexWhere(x => {
          matchNationalId(NationalId(testValue), NationalId(x), originalId = originalId);
        }) match {
          case -1 => {
            false
          }
          case index => {
            true
          }
        }
      }
      case "CI" => {
        val fullExactMatch = processingMetadata.map(_.isNotMoreThan2Pii).getOrElse(false)
        val matchLogic = nationalIdMatchLogicOpt.getOrElse("exact")
        vendorValues.indexWhere(x => {
          if (matchLogic == "exact" || fullExactMatch) matchNationalId(NationalId(testValue), NationalId(x), originalId = originalId, fullExactMatch)
          else if (matchLogic == "partial") matchNationalIdPartial(NationalId(testValue), NationalId(x), originalId = originalId)
          else matchNationalIdFuzzy(NationalId(testValue), NationalId(x), originalId = originalId)
        }) match {
          case -1 => {
            if (vendorValues.nonEmpty) bestMatchedElement("ssn") = vendorValues.head
            else bestMatchedElement("ssn") = ""
            false
          }
          case index => {
            bestMatchedElement("ssn") = vendorValues(index)
            true
          }
        }
      }
      case "PN" => {
        vendorValues.indexWhere(x => matchPN(MobileNumber(testValue), MobileNumber(x))) match {
          case -1 => {
            if (vendorValues.nonEmpty) bestMatchedElement("mobileNumber") = vendorValues.head
            else bestMatchedElement("mobileNumber") = ""
            false
          }
          case index => {
            bestMatchedElement("mobileNumber") = vendorValues(index)
            true
          }
        }
      }
      case "EM" => {
        val result = vendorValues.find(_.equalsIgnoreCase(testValue))
        if (result.nonEmpty) {
          bestMatchedElement("email") = result.get
          true
        } else false
      }
      case "AX" => {
        vendorValues.indexWhere(x => matchStreetAddressOptimized(Some(CleanedStreetAddress(testValue)), Some(StreetAddress(x)), addressMatchLogic, addressComponents)) match {
          case -1 => {
            if (vendorValues.nonEmpty) bestMatchedElement("streetAddress") = vendorValues.head
            else bestMatchedElement("streetAddress") = ""
            false
          }
          case index => {
            bestMatchedElement("streetAddress") = vendorValues(index)
            true
          }
        }
      }
      case "AY" => {
        vendorValues.indexWhere(matchN(testValue, _)) match {
          case -1 => {
            if (vendorValues.nonEmpty) bestMatchedElement("city") = vendorValues.head
            else bestMatchedElement("city") = ""
            false
          }
          case index => {
            bestMatchedElement("city") = vendorValues(index)
            true
          }
        }
      }
      case "AZ" => {
        vendorValues.indexWhere(x => matchZipCode(ZipCode(testValue), ZipCode(x))) match {
          case -1 => {
            if (vendorValues.nonEmpty) bestMatchedElement("zipCode") = vendorValues.head
            else bestMatchedElement("zipCode") = ""
            false
          }
          case index => {
            bestMatchedElement("zipCode") = vendorValues(index)
            true
          }
        }
      }
      case "AS" => {
        vendorValues.indexWhere(matchAS(testValue, _)) match {
          case -1 => {
            if (vendorValues.nonEmpty) bestMatchedElement("state") = vendorValues.head
            else bestMatchedElement("state") = ""
            false
          }
          case index => {
            bestMatchedElement("state") = vendorValues(index)
            true
          }
        }
      }
      case _ => false
    }
  }
}
