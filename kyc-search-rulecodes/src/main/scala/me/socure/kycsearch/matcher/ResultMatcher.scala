package me.socure.kycsearch.matcher

import me.socure.common.kyc.model.PiiAttribute.{ZipCodeMatch, _}
import me.socure.common.kyc.model._
import me.socure.common.kyc.model.es.result.{AddressField, Elastic4sResult, PIIField}
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.kycsearch.matcher.AddressMatchers.cleanStreetAddress
import me.socure.kycsearch.matcher.PhoneNumberMatchers.matchPN
import me.socure.kycsearch.model.weights.KycMatchWeightsGetter
import me.socure.kycsearch.model.{BestKYCMatch, KYCMatchingConfig}
import me.socure.kycsearch.rulecodes.common.NationalIDResolverHelper.{applicantHasMultipleIds, getResolved9DigitSSN}
import org.slf4j.{Logger, LoggerFactory}

import javax.inject.Inject
import scala.util.{Failure, Success, Try}

class ResultMatcher @Inject() (kycMatchWeightsGetter: KycMatchWeightsGetter) {
  private val metrics: Metrics = JavaMetricsFactory.get(classOf[ResultMatcher])

  private val logger: Logger = LoggerFactory.getLogger(getClass)

  def calculateMatches(
                        searchRequestResolved: KYCSearchRequestResolved,
                        foundResults: Elastic4sResult,
                        entity: Boolean,
                        useExactMatch: Boolean = false
                      ): Either[Exception, Array[BestKYCMatch]] = {
    Try {
      val searchRequest = searchRequestResolved.resolvedReq
      val preferences = if (useExactMatch || searchRequest.isSinglePiiRequest) {
        KYCPreferences.exactMatch
      } else {
        if (entity) searchRequest.preferencesEntity else searchRequest.preferencesKyc
      }
      val kycPreferences = if (searchRequest.customPreferencesKyc.customDobMatchLogic == None && !useExactMatch) preferences.dobMatchLogic else searchRequest.customPreferencesKyc.customDobMatchLogic
      val dobMatchLogic = DobMatcherUtil.getDobMatchingLogic(preferences.exactDob, kycPreferences, searchRequestResolved.processingMetadata.isNotMoreThan2Pii)
      val customNameMatchLogic = if (searchRequest.customPreferencesKyc.customNameMatching != None && !useExactMatch) searchRequest.customPreferencesKyc.customNameMatching.get else ""
      val additionalNameMatchingCheck: Boolean = searchRequest.customPreferencesKyc.additionalNameMatchingCheck.getOrElse(false)
      val isSSNExact = preferences.exactSSN
      val originalId = searchRequestResolved.nationalIdResolved.map(_.original).getOrElse(NationalId(""))
      val cleanedStreetAddress = cleanStreetAddress(searchRequest.streetAddress)
      val clusterIdToIdentityRecordMap = foundResults.identityRecords.map(_.map(record => record.clusterId.mkString(",") -> record).toMap).getOrElse(Map.empty)
      val kycsRanked = foundResults.attributes.map{ record =>
        val bestMatchedElementTracker = scala.collection.concurrent.TrieMap[String,String]()
        val results = ResultMatcherHelper.getPiiMatchResultsHelper(searchRequestResolved, record, bestMatchedElementTracker, searchRequest, preferences, dobMatchLogic, customNameMatchLogic, additionalNameMatchingCheck, isSSNExact, originalId, cleanedStreetAddress)
        val identityRecord = record.clusterId.flatMap { cid =>
          clusterIdToIdentityRecordMap.get(cid)
        }
        val inputFN = searchRequestResolved.resolvedReq.firstName.value
        val inputSN = searchRequestResolved.resolvedReq.surName.value
        val hasFNPartialMatch = checkNamePartialMatch(inputFN, record.firstName)
        val hasSNPartialMatch = checkNamePartialMatch(inputSN, record.surName)
        BestKYCMatch(record, results, bestMatchedElementTracker, identityRecord = identityRecord, hasFirstNamePartialMatch = Some(hasFNPartialMatch), hasSurNamePartialMatch = Some(hasSNPartialMatch))
      }.sorted(BestKYCMatchOrdering(searchRequest.modulesEnabled.contains("ModuleDecisioning"), searchRequest.isSinglePiiRequest, searchRequestResolved.processingMetadata.is4DigitSsn, searchRequestResolved).reverse)
      kycsRanked
    } match {
      case Success(bestMatch) => Right(bestMatch)
      case Failure(ex: Exception) => Left(ex)
      case Failure(ex: Throwable) =>
        logger.error("Critical Throwable Error, failing", ex)
        throw ex
    }
  }

  def findBestMatch(
                     searchRequest: KYCSearchRequestResolved,
                     foundResults: Elastic4sResult,
                     entity: Boolean
                   ): Either[Exception, BestKYCMatch] = {
    calculateMatches(searchRequest, foundResults, entity = entity) match {
      case Right(sortedMatches) => Right(sortedMatches.head)
      case Left(ex) => Left(ex)
    }
  }

  def weightedSort(list: Array[BestKYCMatch], isDecisionEnabled: Boolean, isSinglePiiRequest: Boolean, is4DigitSsn: Boolean, searchRequestResolved:KYCSearchRequestResolved): Array[BestKYCMatch] = {
    list.sorted(BestKYCMatchOrdering(isDecisionEnabled, isSinglePiiRequest, is4DigitSsn, searchRequestResolved).reverse)
  }

  class BestKYCMatchOrdering(isDecisionServiceEnabled : Boolean, isSinglePiiRequest: Boolean, is4DigitSsn: Boolean, searchRequestResolved: KYCSearchRequestResolved) extends Ordering[BestKYCMatch] {

    def compare(x: BestKYCMatch, y: BestKYCMatch): Int = {
      weightCompare(x, y, isDecisionServiceEnabled, isSinglePiiRequest, is4DigitSsn, searchRequestResolved)
    }
  }

  private def getNoOfPiis(entity: BestKYCMatch): Int = {
    var noOfPiis = 0
    val record = entity.cluster
    if (record.firstName.exists(_.nonEmpty)) noOfPiis = noOfPiis + 1
    if (record.middleName.exists(_.nonEmpty)) noOfPiis = noOfPiis + 1
    if (record.surName.exists(_.nonEmpty)) noOfPiis = noOfPiis + 1
    if (record.suffixName.exists(_.nonEmpty)) noOfPiis = noOfPiis + 1
    if (record.dob.exists(_.nonEmpty)) noOfPiis = noOfPiis + 1
    if (record.ssn.exists(_.nonEmpty)) noOfPiis = noOfPiis + 1
    if (record.mobileNumber.exists(_.nonEmpty)) noOfPiis = noOfPiis + 1
    if (record.emailAddress.exists(_.nonEmpty)) noOfPiis = noOfPiis + 1
    if (record.streetAddress.exists(_.nonEmpty)) noOfPiis = noOfPiis + 1
    if (record.city.exists(_.nonEmpty)) noOfPiis = noOfPiis + 1
    if (record.state.exists(_.nonEmpty)) noOfPiis = noOfPiis + 1
    if (record.zipCode.exists(_.nonEmpty)) noOfPiis = noOfPiis + 1
    noOfPiis
  }

  private def containsName(entity: BestKYCMatch): Boolean = {
    val record = entity.cluster
    record.firstName.exists(_.nonEmpty) & record.surName.exists(_.nonEmpty)
  }

  private def comparePiiWeight(entity1: BestKYCMatch, entity2: BestKYCMatch): Int = {
    def compare(o1: (String, String), o2: (String, String)) = {
      val value1 = o1._1
      val rank1 = o1._2

      val value2 = o2._1
      val rank2 = o2._2

      val res = value1.compareTo(value2)
      if (res > 0) 1
      else if (res < 0) -1
      else {
        if (rank2.compareTo(rank1) > 0) 1
        else if (rank2.compareTo(rank1) < 0) -1
        else 0
      }
    }

    def hasMatch(entity: BestKYCMatch, piiAttributes: PiiAttribute*): Boolean = {
      piiAttributes.exists(piiAttribute => entity.piiMatchResults.get(piiAttribute).contains(1))
    }

    def getPhoneLastSeenAndRank(entity: BestKYCMatch): (String, String) = {
      if (hasMatch(entity, MobileNumberMatch)) {
        val aliases: Seq[PIIField] = entity.identityRecord.map(_.phoneNumber).toSeq.flatten
        val matchedValue = entity.bestMatchedElements.getOrElse("mobileNumber", "")
        aliases.find(alias => matchPN(MobileNumber(alias.value), MobileNumber(matchedValue)))
          .orElse(aliases.headOption)
          .map(e => (e.lastSeen.orElse(e.firstSeen).getOrElse(""), e.rank.getOrElse("")))
          .getOrElse("", "")
      } else ("", "")
    }

    def getEmailLastSeenAndRank(entity: BestKYCMatch): (String, String) = {
      if (hasMatch(entity, EmailMatch)) {
        val aliases: Seq[PIIField] = entity.identityRecord.map(_.email).toSeq.flatten
        val matchedValue = entity.bestMatchedElements.getOrElse("email", "")
        aliases.find(_.value.equals(matchedValue))
          .orElse(aliases.headOption)
          .map(e => (e.lastSeen.orElse(e.firstSeen).getOrElse(""), e.rank.getOrElse("")))
          .getOrElse("", "")
      } else ("", "")
    }

    def getAddressLastSeenAndRank(entity: BestKYCMatch): (String, String) = {
      if (hasMatch(entity, StreetAddressMatch)) {
        val aliases: Seq[AddressField] = entity.identityRecord.map(_.address).toSeq.flatten
        val matchedValue = entity.bestMatchedElements.getOrElse("streetAddress", "")
        aliases.find(_.street.equals(matchedValue))
          .orElse(aliases.headOption)
          .map(e => (e.lastSeen.orElse(e.firstSeen).getOrElse(""), e.rank.getOrElse("")))
          .getOrElse("", "")
      } else ("", "")
    }

    val noOfPiisEntity1 = getNoOfPiis(entity1)
    val noOfPiisEntity2 = getNoOfPiis(entity2)
    val entity1containsName = containsName(entity1)
    val entity2containsName = containsName(entity2)
    if (entity1containsName && !entity2containsName) 1 // choose entity 1
    else if (!entity1containsName && entity2containsName) -1 // choose entity 2
    else {
      val score = Seq(
        compare(getPhoneLastSeenAndRank(entity1), getPhoneLastSeenAndRank(entity2)),
        compare(getEmailLastSeenAndRank(entity1), getEmailLastSeenAndRank(entity2)),
        compare(getAddressLastSeenAndRank(entity1), getAddressLastSeenAndRank(entity2))
      ).sum
      if (score == 0) {
        if (noOfPiisEntity1 > noOfPiisEntity2) 1 // choose entity 1
        else if (noOfPiisEntity1 < noOfPiisEntity2) -1 // choose entity 2
        else 0
      } else score
    }
  }

   implicit object  BestKYCMatchOrdering {
     def apply(isDecisionServiceEnabled: Boolean, isSinglePiiRequest: Boolean, is4DigitSsn: Boolean, searchRequestResolved: KYCSearchRequestResolved): BestKYCMatchOrdering = new BestKYCMatchOrdering(isDecisionServiceEnabled, isSinglePiiRequest, is4DigitSsn, searchRequestResolved)
   }

  def getWeight(entity: BestKYCMatch, isDecisionEnabled: Boolean, is4DigitSsn: Boolean, searchRequestResolved: KYCSearchRequestResolved): Double = {
    val matchWeight = getMatchWeight(isDecisionEnabled)
    val bestMatchSSN = entity.cluster.ssn
    val score = entity.piiMatchResults.filter {
      case (_, isMatched) =>
        isMatched == 1
    }.foldLeft(0.0)((scoreAcc, elem) => {
      elem._1 match {
        case FirstNameMatch => scoreAcc + matchWeight.firstNameWeight
        case SurNameMatch => scoreAcc + matchWeight.lastNameWeight
        case SSNMatch => {

          val ssnNegativeWeight = searchRequestResolved.nationalIdResolved match {
            case Some(NationalIdResolved(originalNationalId, nationalId, cleanedNationalId, isValid)) => {
              val resolvedSSNOpt = Some(getResolved9DigitSSN(cleanedNationalId, entity))
              val cleanedResolvedNationalId = resolvedSSNOpt.getOrElse(cleanedNationalId)
              val hasMultipleApplicationIds = applicantHasMultipleIds(isNationalIdValid = isValid, originalNationalId = originalNationalId, cleanedResolvedNationalId, entity)
              if(hasMultipleApplicationIds){
                -2
              }
              else {
                0
              }
            }
            case _=> {
              0
            }
          }
          if (is4DigitSsn) scoreAcc + matchWeight.ssn4Weight + ssnNegativeWeight else scoreAcc + matchWeight.ssnWeight + ssnNegativeWeight
        }
        case SSN4Match => if (is4DigitSsn) scoreAcc + matchWeight.ssn4Weight else scoreAcc
        case DOBMatch => scoreAcc + matchWeight.dobWeight
        case MobileNumberMatch => scoreAcc + matchWeight.phoneWeight
        case EmailMatch => scoreAcc + matchWeight.emailWeight
        case StreetAddressMatch => scoreAcc + matchWeight.streetAddressWeight
        case CityMatch => scoreAcc + matchWeight.cityWeight
        case ZipCodeMatch => scoreAcc + matchWeight.zipWeight
        case StateMatch => scoreAcc + matchWeight.stateWeight
        case _ => throw new Exception("Invalid PII Attribute")
      }
    })
    score / matchWeight.getSum(is4DigitSsn)
  }

  private def checkNamePartialMatch(inputName: String, nameAliases: Array[String]): Boolean = {
    nameAliases.exists(nameAlias => NameMatchers.matchNRelaxed(inputName, nameAlias))
  }

  /**
   * > 0 if entity1 greater than entity2
   * < 0 if entity2 greater than entity1
   * 0 if both are equal
   */
   def weightCompare(entity1: BestKYCMatch, entity2: BestKYCMatch, isDecisionEnabled : Boolean, isSinglePiiRequest: Boolean, is4DigitSsn: Boolean, searchRequestResolved: KYCSearchRequestResolved): Int = {

     def compareBoolean(e1: Boolean, e2: Boolean):Int = {
       if (e1 && e2) 0
       else if (e1) 1
       else if (e2) -1
       else 0
     }

     val weight1 = getWeight(entity1, isDecisionEnabled, is4DigitSsn, searchRequestResolved)
     val weight2 = getWeight(entity2, isDecisionEnabled, is4DigitSsn, searchRequestResolved)
     if (weight1 > weight2) 1
     else if (weight1 < weight2) -1
     else if (isSinglePiiRequest) {
       comparePiiWeight(entity1, entity2)
     } else {
       val inputFN = searchRequestResolved.resolvedReq.firstName.value
       val inputSN = searchRequestResolved.resolvedReq.surName.value

       val isEntity1FNTrue = entity1.hasFirstNamePartialMatch.getOrElse(checkNamePartialMatch(inputFN, entity1.cluster.firstName))
       val isEntity2FNTrue = entity2.hasFirstNamePartialMatch.getOrElse(checkNamePartialMatch(inputFN, entity2.cluster.firstName))
       val checkFN = compareBoolean(isEntity1FNTrue, isEntity2FNTrue)
       if(checkFN == 0) {
         val isEntity1SNTrue = entity1.hasSurNamePartialMatch.getOrElse(checkNamePartialMatch(inputSN, entity1.cluster.surName))
         val isEntity2SNTrue = entity2.hasSurNamePartialMatch.getOrElse(checkNamePartialMatch(inputSN, entity2.cluster.surName))
         compareBoolean(isEntity1SNTrue, isEntity2SNTrue)
       }
       else checkFN
     }
  }


  def getMatchWeight(isDecisionEnabled: Boolean)() : KYCMatchingConfig = {
    val matchWeights:Map[String,KYCMatchingConfig] = kycMatchWeightsGetter.get
    val matchweight : KYCMatchingConfig = {
      if(isDecisionEnabled) {
        matchWeights("challenger-match-weights")
      }
      else {
        matchWeights("default-match-weights")
      }
    }
    matchweight
  }

}
