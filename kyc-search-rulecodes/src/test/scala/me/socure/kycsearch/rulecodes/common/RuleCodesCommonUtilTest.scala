package me.socure.kycsearch.rulecodes.common

import com.socure.domain.socure.scoring.RuleCodes._
import me.socure.common.kyc.model._
import me.socure.kycsearch.rulecodes.RuleCodesCommonUtil.generateConfidenceScore
import me.socure.kycsearch.rulecodes.SampleRecords.request
import org.scalatest.{FreeSpec, Matchers}

class RuleCodesCommonUtilTest extends FreeSpec with Matchers {

  // Base request for testing
  private val baseRequest = request.copy(email=Some("<EMAIL>"))

  private def createSearchRequestResolved(
                                           request: KycEntitySearchRequest,
                                           is4DigitSsn: Boolean = false
                                         ): KYCSearchRequestResolved = {
    KYCSearchRequestResolved(
      originalReq = request,
      resolvedReq = request,
      nationalIdResolved = request.nationalId.map(nid => NationalIdResolved(
        original = nid,
        input = nid,
        cleaned = nid,
        isValid = true
      )),
      processingMetadata = KycProcessingMetadata(
        inputPiiCount = 3,
        isNotMoreThan2Pii = false,
        is4DigitSsn = is4DigitSsn
      )
    )
  }

  "Should generate confidence scores correctly for all scenarios" - {
    Seq(
      // Test case 1: Empty request - should return empty map
      (1,
        Map.empty[String, Double],
        baseRequest.copy(firstName = FirstName(""), surName = SurName(""), nationalId = None, dob = None),
        false,
        Map.empty[String, Double]),

      // Test case 2: All positive matches with full SSN
      (2,
        Map(
          EX_FIRSTNAME_SCORE.code.toString -> 1.0,
          EX_LASTNAME_SCORE.code.toString -> 1.0,
          EX_PHONE_SCORE.code.toString -> 1.0,
          EX_DOB_SCORE.code.toString -> 1.0,
          EX_EMAIL_SCORE.code.toString -> 1.0,
          EX_STREETADDRESS_SCORE.code.toString -> 1.0,
          EX_CITY_SCORE.code.toString -> 1.0,
          EX_STATE_SCORE.code.toString -> 1.0,
          EX_SSN_SCORE.code.toString -> 1.0
        ),
        baseRequest,
        false,
        Map(EX_CONFIDENCE_SCORE.code.toString -> 0.99)),

        (3,
          Map(
            EX_FIRSTNAME_SCORE.code.toString -> 1.0,
            EX_LASTNAME_SCORE.code.toString -> 1.0,
            EX_PHONE_SCORE.code.toString -> 1.0,
            EX_DOB_SCORE.code.toString -> 0.01,
            EX_FIRST_NAME_POSSIBLY_MISTYPED.code.toString -> 0.99,
            EX_EMAIL_SCORE.code.toString -> 1.0,
            EX_STREETADDRESS_SCORE.code.toString -> 1.0,
            EX_CITY_SCORE.code.toString -> 1.0,
            EX_STATE_SCORE.code.toString -> 1.0,
            EX_SSN_SCORE.code.toString -> 1.0
          ),
          baseRequest,
          false,
          Map(EX_CONFIDENCE_SCORE.code.toString -> 0.815)),

          (4,
            Map(
              EX_FIRSTNAME_SCORE.code.toString -> 1.0,
              EX_LASTNAME_SCORE.code.toString -> 1.0,
              EX_PHONE_SCORE.code.toString -> 1.0,
              EX_DOB_SCORE.code.toString -> 0.01,
              EX_FIRST_NAME_POSSIBLY_MISTYPED.code.toString -> 0.99,
              EX_EMAIL_SCORE.code.toString -> 1.0,
              EX_STREETADDRESS_SCORE.code.toString -> 1.0,
              EX_CITY_SCORE.code.toString -> 1.0,
              EX_STATE_SCORE.code.toString -> 1.0,
              EX_SSN_SCORE.code.toString -> 1.0
            ),
            baseRequest,
            true,
            Map(EX_CONFIDENCE_SCORE.code.toString -> 0.76875)),

            (5,
              Map(
                EX_FIRSTNAME_SCORE.code.toString -> 1.0,
                EX_LASTNAME_SCORE.code.toString -> 1.0,
                EX_DOB_SCORE.code.toString -> 0.01,
                EX_FIRST_NAME_POSSIBLY_MISTYPED.code.toString -> 0.99,
                EX_CITY_SCORE.code.toString -> 1.0,
                EX_STATE_SCORE.code.toString -> 1.0,
                EX_SSN_SCORE.code.toString -> 1.0
              ),
              baseRequest,
              true,
              Map(EX_CONFIDENCE_SCORE.code.toString -> 0.65625)),

              (6,
                Map(
                  EX_FIRSTNAME_SCORE.code.toString -> 1.0,
                  EX_LASTNAME_SCORE.code.toString -> 1.0,
                  EX_DOB_SCORE.code.toString -> 0.01,
                  EX_FIRST_NAME_POSSIBLY_MISTYPED.code.toString -> 0.99,
                  EX_CITY_SCORE.code.toString -> 1.0,
                  EX_STATE_SCORE.code.toString -> 1.0,
                  EX_SSN_SCORE.code.toString -> 1.0
                ),
                baseRequest.copy(email=None, mobileNumber = None, streetAddress = None ),
                true,
                Map(EX_CONFIDENCE_SCORE.code.toString -> 0.75))

    ).foreach {
      case (sno: Int, ruleCodes: Map[String, Double], request: KycEntitySearchRequest, is4DigitSsn: Boolean, expectedResult: Map[String, Double]) =>
        s"case: $sno" in {
          val searchRequest = createSearchRequestResolved(request, is4DigitSsn)
          val result = generateConfidenceScore(ruleCodes, searchRequest)

          if (expectedResult.isEmpty) {
            result shouldBe Map.empty
          } else {
            result should contain key EX_CONFIDENCE_SCORE.code.toString
            // For non-empty results, we check if the score is in the expected range
            // since the exact calculation might vary slightly
            expectedResult.get(EX_CONFIDENCE_SCORE.code.toString) match {
              case Some(expectedScore) if expectedScore > 0 =>
                result(EX_CONFIDENCE_SCORE.code.toString) shouldBe expectedScore
              case None => // No specific expectation
            }
          }
        }
    }
  }

  "Should handle edge cases correctly" - {
    "case: nationalIdResolved is None" in {
      val ruleCodes = Map(EX_FIRSTNAME_SCORE.code.toString -> 1.0)
      val searchRequest = KYCSearchRequestResolved(
        originalReq = baseRequest,
        resolvedReq = baseRequest,
        nationalIdResolved = None
      )

      val result = generateConfidenceScore(ruleCodes, searchRequest)
      result shouldBe Map.empty
    }

    "case: all required fields empty" in {
      val ruleCodes = Map(EX_FIRSTNAME_SCORE.code.toString -> 1.0)
      val emptyRequest = baseRequest.copy(
        firstName = FirstName(""),
        surName = SurName(""),
        nationalId = None,
        dob = None
      )
      val searchRequest = createSearchRequestResolved(emptyRequest)

      val result = generateConfidenceScore(ruleCodes, searchRequest)
      result shouldBe Map.empty
    }
  }
}