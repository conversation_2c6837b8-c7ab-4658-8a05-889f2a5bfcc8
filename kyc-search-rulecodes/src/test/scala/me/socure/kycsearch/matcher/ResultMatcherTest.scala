package me.socure.kycsearch.matcher

import java.io.InputStreamReader

import com.opencsv.CSVReaderHeaderAware
import me.socure.common.kyc.model.PiiAttribute._
import me.socure.common.kyc.model._
import me.socure.common.kyc.model.es.result.Records
import me.socure.kycsearch._
import me.socure.kycsearch.matcher.DOBMatchers._
import me.socure.kycsearch.matcher.NameMatchers._
import me.socure.kycsearch.matcher.NationalIDMacthers._
import me.socure.kycsearch.model.BestKYCMatch
import me.socure.kycsearch.rulecodes.SampleRecords.ElasticSearchIdentityRecord
import me.socure.service.constants.DobMatchLogic
import org.apache.commons.lang3.SerializationUtils
import org.scalatest.{FreeSpec, Matchers}
import me.socure.kycsearch.rulecodes.SampleRecords.matchWeightsGetter

class ResultMatcherTest extends FreeSpec with Matchers {
  private val resultMatcher = new ResultMatcher(matchWeightsGetter)
  private val request = KycEntitySearchRequest(
    firstName = FirstName("ashley"),
    middleName = Some(MiddleName("N")),
    surName = SurName("paul"),
    streetAddress = Some(StreetAddress("602 ne bush prairie private se unit 12345")),
    city = Some(City("youngville")),
    zipCode = Some(ZipCode("72100")),
    zip4 = None,
    latitude = None,
    longitude = None,
    state = Some(State("la")),
    mobileNumber = Some(MobileNumber("**********")),
    nationalId = Some(NationalId("796040440")),
    dob = Some(DOB("20030208")),
    driverLicense = None,
    preferencesKyc = KYCPreferences(
      exactDob = true,
      exactSSN = true,
      dobMatchLogic = Some("exact_yyyy_mm_dd")
    ),
    customPreferencesKyc = CustomKYCPreferences(
      customDobMatchLogic = None,
      maxAddressCount = None,
      customNameMatching = None,
      maxEmailCount = None,
      maxPhoneCount = None
    ),
    preferencesEntity = KYCPreferences(
      exactDob = true,
      exactSSN = true,
      dobMatchLogic = Some("exact_yyyy_mm_dd")
    ),
    maskPii = false,
    workflows = Workflows.All,
    modulesEnabled = Set("KYC", "ModuleDecisioning")
  )

  private val bestMatch = BestKYCMatch(cluster = ElasticSearchIdentityRecord.attributes.head,
    piiMatchResults = Map(FirstNameMatch->1, SurNameMatch->1, SSNMatch-> 1,
    DOBMatch->1,MobileNumberMatch->1,StreetAddressMatch-> 1,
    CityMatch-> 1, ZipCodeMatch->1, StateMatch->1, EmailMatch -> 0), bestMatchedElements = scala.collection.concurrent.TrieMap("surName" -> "paul", "ssn" -> "796040440", "city" -> "youngville", "mobileNumber" -> "**********", "state" -> "la", "zipCode" -> "72100", "dob" -> "20030208", "streetAddress" -> "602 ne bush prairie private se unit 12345", "middleName" -> "N", "firstName" -> "ashley"), hasFirstNamePartialMatch = Some(true), hasSurNamePartialMatch = Some(true))

  "ResultMatcher" - {

    "should sort correctly" in {
      val matchedRes = resultMatcher.findBestMatch(searchRequest = request.resolve(), ElasticSearchIdentityRecord, entity = false)
      matchedRes match {
        case Right(matchSuccess) =>
          matchSuccess shouldBe bestMatch
        case Left(exception) =>
          fail(exception)
      }
    }

    "should not match DOB without radius" in {
      val withoutDOBBestMatch = BestKYCMatch(cluster = ElasticSearchIdentityRecord.attributes.head, piiMatchResults = Map(FirstNameMatch->1, SurNameMatch->1, SSNMatch-> 1,
        DOBMatch->0,MobileNumberMatch->1,StreetAddressMatch-> 1,
        CityMatch-> 1, ZipCodeMatch->1, StateMatch->1, EmailMatch -> 0),bestMatchedElements = scala.collection.concurrent.TrieMap("surName" -> "paul", "ssn" -> "796040440", "city" -> "youngville", "mobileNumber" -> "**********", "state" -> "la", "zipCode" -> "72100", "dob" -> "20030208", "streetAddress" -> "602 ne bush prairie private se unit 12345", "middleName" -> "N", "firstName" -> "ashley"), hasFirstNamePartialMatch = Some(true), hasSurNamePartialMatch = Some(true))

      val requestDOB = request.copy(dob = Some(DOB("20030218")))
      val matchedRes = resultMatcher.findBestMatch(searchRequest = requestDOB.resolve(), ElasticSearchIdentityRecord, entity = false)
      matchedRes match {
        case Right(matchSuccess) =>
          matchSuccess shouldBe withoutDOBBestMatch
        case Left(exception) =>
          fail(exception)
      }
    }

    "should match DOB with radius" in {
      val requestDOB = request.copy(dob = Some(DOB("20030218")), preferencesKyc = KYCPreferences(exactDob = false, dobMatchLogic = Option(DobMatchLogic.FUZZY_MATCH.toString), exactSSN = true))
      val matchedRes = resultMatcher.findBestMatch(searchRequest = requestDOB.resolve(), ElasticSearchIdentityRecord, entity = false)
      matchedRes match {
        case Right(matchSuccess) =>
          matchSuccess shouldBe bestMatch
        case Left(exception) =>
          fail(exception)
      }
    }

    "should match DOB - exact YYYY_MM match" in {
      val requestDOB = request.copy(dob = Some(DOB("20030218")), preferencesKyc = KYCPreferences(exactDob = false, dobMatchLogic = Option(DobMatchLogic.EXACT_YYYY_MM_MATCH.toString), exactSSN = true))
      val matchedRes = resultMatcher.findBestMatch(searchRequest = requestDOB.resolve(), ElasticSearchIdentityRecord, entity = false)
      matchedRes match {
        case Right(matchSuccess) =>
          matchSuccess shouldBe bestMatch
        case Left(exception) =>
          fail(exception)
      }
    }

    "should match DOB - fuzzy YYYY_MM match" in {
      val requestDOB = request.copy(dob = Some(DOB("20040218")), preferencesKyc = KYCPreferences(exactDob = false, dobMatchLogic = Option(DobMatchLogic.FUZZY_YYYY_MM_MATCH.toString), exactSSN = true))
      val matchedRes = resultMatcher.findBestMatch(searchRequest = requestDOB.resolve(), ElasticSearchIdentityRecord, entity = false)
      matchedRes match {
        case Right(matchSuccess) =>
          matchSuccess shouldBe bestMatch
        case Left(exception) =>
          fail(exception)
      }
    }

    "should match DOB - fuzzy and 2 digit transposition YYYY_MM_DD match" in {
      val requestDOB = request.copy(dob = Some(DOB("20040205")), preferencesKyc = KYCPreferences(exactDob = false, dobMatchLogic = Option(DobMatchLogic.FUZZY_TWO_DIGIT_TRANSPOSITION_YYYY_MM_DD_MATCH.toString), exactSSN = true))
      val matchedRes = resultMatcher.findBestMatch(searchRequest = requestDOB.resolve(), ElasticSearchIdentityRecord, entity = false)
      matchedRes match {
        case Right(matchSuccess) =>
          matchSuccess shouldBe bestMatch
        case Left(exception) =>
          fail(exception)
      }
    }

    "should match DOB - fuzzy and 2 digit transposition YYYY_MM_DD match - no match" in {
      val requestDOB = request.copy(dob = Some(DOB("20040228")), preferencesKyc = KYCPreferences(exactDob = false, dobMatchLogic = Option(DobMatchLogic.FUZZY_TWO_DIGIT_TRANSPOSITION_YYYY_MM_DD_MATCH.toString), exactSSN = true))
      val matchedRes = resultMatcher.findBestMatch(searchRequest = requestDOB.resolve(), ElasticSearchIdentityRecord, entity = false)
      matchedRes match {
        case Right(matchSuccess) =>
          matchSuccess.piiMatchResults shouldBe Map(FirstNameMatch->1, SurNameMatch->1, SSNMatch-> 1,
            DOBMatch->0,MobileNumberMatch->1,StreetAddressMatch-> 1,
            CityMatch-> 1, ZipCodeMatch->1, StateMatch->1, EmailMatch -> 0)
        case Left(exception) =>
          fail(exception)
      }
    }

    "should match DOB - fuzzy and 2 digit transposition YYYY_MM match" in {
      val requestDOB = request.copy(dob = Some(DOB("20040128")), preferencesKyc = KYCPreferences(exactDob = false, dobMatchLogic = Option(DobMatchLogic.FUZZY_TWO_DIGIT_TRANSPOSITION_YYYY_MM_MATCH.toString), exactSSN = true))
      val matchedRes = resultMatcher.findBestMatch(searchRequest = requestDOB.resolve(), ElasticSearchIdentityRecord, entity = false)
      matchedRes match {
        case Right(matchSuccess) =>
          matchSuccess shouldBe bestMatch
        case Left(exception) =>
          fail(exception)
      }
    }

    "should match DOB - fuzzy 2 digit transposition YYYY match" in {
      val requestDOB = request.copy(dob = Some(DOB("20300128")), preferencesKyc = KYCPreferences(exactDob = false, dobMatchLogic = Option(DobMatchLogic.TWO_DIGIT_TRANSPOSITION_YYYY_MATCH.toString), exactSSN = true))
      val matchedRes = resultMatcher.findBestMatch(searchRequest = requestDOB.resolve(), ElasticSearchIdentityRecord, entity = false)
      matchedRes match {
        case Right(matchSuccess) =>
          matchSuccess shouldBe bestMatch
        case Left(exception) =>
          fail(exception)
      }
    }

    "should match DOB - when MatchLogic - exact_yyyy_swapped_mm_dd and DOB provided is as in Sample ES Record" in {
      val requestDOB = request.copy(dob = Some(DOB("20030208")), preferencesKyc = KYCPreferences(exactDob = false, dobMatchLogic = Option(DobMatchLogic.EXACT_YYYY_SWAPPED_MM_DD_MATCH.toString), exactSSN = true))
      val matchedRes = resultMatcher.findBestMatch(searchRequest = requestDOB.resolve(), ElasticSearchIdentityRecord, entity = false)
      matchedRes match {
        case Right(matchSuccess) =>
          matchSuccess shouldBe bestMatch
        case Left(exception) =>
          fail(exception)
      }
    }

    "should match DOB - when MatchLogic - exact_yyyy_swapped_mm_dd and DOB provided has MM,DD swapped" in {
      val requestDOB = request.copy(dob = Some(DOB("20030802")), preferencesKyc = KYCPreferences(exactDob = false, dobMatchLogic = Option(DobMatchLogic.EXACT_YYYY_SWAPPED_MM_DD_MATCH.toString), exactSSN = true))
      val matchedRes = resultMatcher.findBestMatch(searchRequest = requestDOB.resolve(), ElasticSearchIdentityRecord, entity = false)
      matchedRes match {
        case Right(matchSuccess) =>
          matchSuccess shouldBe bestMatch
        case Left(exception) =>
          fail(exception)
      }
    }

    "should not match DOB - when MatchLogic - exact_yyyy_swapped_mm_dd and DOB provided has wrong MM/DD" in {
      val bestMatchWithNoDob =
        BestKYCMatch(
          cluster = ElasticSearchIdentityRecord.attributes.head,
          piiMatchResults =
            Map(
              FirstNameMatch -> 1,
              SurNameMatch -> 1,
              SSNMatch -> 1,
              DOBMatch -> 0,
              MobileNumberMatch -> 1,
              StreetAddressMatch -> 1,
              CityMatch -> 1,
              ZipCodeMatch -> 1,
              StateMatch -> 1,
              EmailMatch -> 0
              ),
          bestMatchedElements =
            scala.collection.concurrent.TrieMap(
              "surName" -> "paul",
              "ssn" -> "796040440",
              "city" -> "youngville",
              "mobileNumber" -> "**********",
              "state" -> "la",
              "zipCode" -> "72100",
              "dob" -> "20030208",
              "streetAddress" -> "602 ne bush prairie private se unit 12345",
              "middleName" -> "N",
              "firstName" -> "ashley"
              ), hasFirstNamePartialMatch = Some(true), hasSurNamePartialMatch = Some(true)
          )

      val requestDOB = request.copy(dob = Some(DOB("20030902")), preferencesKyc = KYCPreferences(exactDob = false, dobMatchLogic = Option(DobMatchLogic.EXACT_YYYY_SWAPPED_MM_DD_MATCH.toString), exactSSN = true))
      val matchedRes = resultMatcher.findBestMatch(searchRequest = requestDOB.resolve(), ElasticSearchIdentityRecord, entity = false)
      matchedRes match {
        case Right(matchSuccess) =>
          matchSuccess shouldBe bestMatchWithNoDob
        case Left(exception) =>
          fail(exception)
      }
    }

    "should not match DOB - when MatchLogic - exact_yyyy_swapped_mm_dd and DOB provided has swapped MM/DD but wrong YYYY" in {
      val bestMatchWithNoDob =
        BestKYCMatch(
          cluster = ElasticSearchIdentityRecord.attributes.head,
          piiMatchResults =
            Map(
              FirstNameMatch -> 1,
              SurNameMatch -> 1,
              SSNMatch -> 1,
              DOBMatch -> 0,
              MobileNumberMatch -> 1,
              StreetAddressMatch -> 1,
              CityMatch -> 1,
              ZipCodeMatch -> 1,
              StateMatch -> 1,
              EmailMatch -> 0
              ),
          bestMatchedElements =
            scala.collection.concurrent.TrieMap(
              "surName" -> "paul",
              "ssn" -> "796040440",
              "city" -> "youngville",
              "mobileNumber" -> "**********",
              "state" -> "la",
              "zipCode" -> "72100",
              "dob" -> "20030208",
              "streetAddress" -> "602 ne bush prairie private se unit 12345",
              "middleName" -> "N",
              "firstName" -> "ashley"
              ), hasFirstNamePartialMatch = Some(true), hasSurNamePartialMatch = Some(true)
          )

      val requestDOB = request.copy(dob = Some(DOB("20040802")), preferencesKyc = KYCPreferences(exactDob = false, dobMatchLogic = Option(DobMatchLogic.EXACT_YYYY_SWAPPED_MM_DD_MATCH.toString), exactSSN = true))
      val matchedRes = resultMatcher.findBestMatch(searchRequest = requestDOB.resolve(), ElasticSearchIdentityRecord, entity = false)
      matchedRes match {
        case Right(matchSuccess) =>
          matchSuccess shouldBe bestMatchWithNoDob
        case Left(exception) =>
          fail(exception)
      }
    }

    "should match DOB - Cashapp CUSTOM_DOB_LOGIC_1001" in {
      Seq(
        ("1982-09-12", "1982-09-12"), //YYYY-MM-DD
        ("1982-09-00", "1982-09-12"), //YYYY-MM-00
        ("1982-00-12", "1982-09-12"), //YYYY-00-DD
        ("0000-09-12", "1982-09-12")  //0000-MM-DD
      ).foreach {
        case (esDOBString, inputDOBString) => {
          val requestDOB = request.copy(dob = Some(DOB(inputDOBString)), preferencesKyc = KYCPreferences(exactDob = false, dobMatchLogic = Option(DobMatchLogic.CUSTOM_DOB_LOGIC_1001.toString), exactSSN = true))
          val dobModifiedEsData = SerializationUtils.clone(ElasticSearchIdentityRecord.attributes).asInstanceOf[Array[Records]]
            .map(_.copy(dob = Array(esDOBString)))
          val dobModifiedIdentityRecord = ElasticSearchIdentityRecord.copy(
            attributes = dobModifiedEsData
          )
          val matchedRes = resultMatcher.findBestMatch(searchRequest = requestDOB.resolve(), dobModifiedIdentityRecord, entity = false)
          val expected = BestKYCMatch(cluster = dobModifiedIdentityRecord.attributes.head,
            piiMatchResults = Map(FirstNameMatch->1, SurNameMatch->1, SSNMatch-> 1,
              DOBMatch->1,MobileNumberMatch->1,StreetAddressMatch-> 1,
              CityMatch-> 1, ZipCodeMatch->1, StateMatch->1, EmailMatch -> 0), bestMatchedElements = scala.collection.concurrent.TrieMap("surName" -> "paul", "ssn" -> "796040440", "city" -> "youngville", "mobileNumber" -> "**********", "state" -> "la", "zipCode" -> "72100", "dob" -> esDOBString, "streetAddress" -> "602 ne bush prairie private se unit 12345", "middleName" -> "N", "firstName" -> "ashley"), hasFirstNamePartialMatch = Some(true), hasSurNamePartialMatch = Some(true))
          matchedRes match {
            case Right(matchSuccess) =>
              matchSuccess shouldBe expected
            case Left(exception) =>
              fail(exception)
          }
        }
      }
    }

    "shouldn't match DOB - Cashapp CUSTOM_DOB_LOGIC_1001 when there is no exact match" in {
      Seq(
        ("1982-09-12", "1982-08-12"), //YYYY-MM-DD
        ("1982-09-00", "1982-08-12"), //YYYY-MM-00
        ("1982-00-12", "1982-09-13"), //YYYY-00-DD
        ("1982-00-00", "1983-09-12"), //YYYY-00-00
        ("0000-09-12", "1982-09-13"),  //0000-MM-DD
        ("1982-00-00", "1982-09-12"), //YYYY-00-00
        ("1990-01-01", "1990-00-00"), //YYYY match should be rejected
        ("1990-01-01", "1990-02-00"), //YYYY match should be rejected
        ("1990-01-01", "1990-00-04") //YYYY match should be rejected
      ).foreach {
        case (esDOBString, inputDOBString) => {
          val requestDOB = request.copy(dob = Some(DOB(inputDOBString)),firstName = FirstName("test"), preferencesKyc = KYCPreferences(exactDob = false, dobMatchLogic = Option(DobMatchLogic.CUSTOM_DOB_LOGIC_1001.toString), exactSSN = true))
          val dobModifiedEsData = SerializationUtils.clone(ElasticSearchIdentityRecord.attributes).asInstanceOf[Array[Records]]
            .map(_.copy(dob = Array(esDOBString)))
          val dobModifiedIdentityRecord = ElasticSearchIdentityRecord.copy(
            attributes = dobModifiedEsData
          )
          val matchedRes = resultMatcher.findBestMatch(searchRequest = requestDOB.resolve(), dobModifiedIdentityRecord, entity = false)
          val expected = BestKYCMatch(cluster = dobModifiedIdentityRecord.attributes.head,
            piiMatchResults = Map(FirstNameMatch->0, SurNameMatch->1, SSNMatch-> 1,
              DOBMatch->0,MobileNumberMatch->1,StreetAddressMatch-> 1,
              CityMatch-> 1, ZipCodeMatch->1, StateMatch->1, EmailMatch -> 0), bestMatchedElements = scala.collection.concurrent.TrieMap("surName" -> "paul", "ssn" -> "796040440", "city" -> "youngville", "mobileNumber" -> "**********", "state" -> "la", "zipCode" -> "72100", "dob" -> esDOBString, "streetAddress" -> "602 ne bush prairie private se unit 12345", "middleName" -> "N", "firstName" -> "ashley"), hasFirstNamePartialMatch = Some(false), hasSurNamePartialMatch = Some(true))
          matchedRes match {
            case Right(matchSuccess) =>
              matchSuccess shouldBe expected
            case Left(exception) =>
              fail(exception)
          }
        }
      }
    }

    "should match DOB - Cashapp CUSTOM_DOB_LOGIC_1001 with other PII mismatch" in {
      Seq(
        ("1982-09-12", "1982-09-12"), //YYYY-MM-DD
        ("1982-09-00", "1982-09-12"), //YYYY-MM-00
        ("1982-00-12", "1982-09-12"), //YYYY-00-DD
        ("0000-09-12", "1982-09-12")  //0000-MM-DD
      ).foreach {
        case (esDOBString, inputDOBString) => {
          val requestDOB = request.copy(dob = Some(DOB(inputDOBString)),firstName = FirstName("test"), preferencesKyc = KYCPreferences(exactDob = false, dobMatchLogic = Option(DobMatchLogic.CUSTOM_DOB_LOGIC_1001.toString), exactSSN = true))
          val dobModifiedEsData = SerializationUtils.clone(ElasticSearchIdentityRecord.attributes).asInstanceOf[Array[Records]]
            .map(_.copy(dob = Array(esDOBString)))
          val dobModifiedIdentityRecord = ElasticSearchIdentityRecord.copy(
            attributes = dobModifiedEsData
          )
          val matchedRes = resultMatcher.findBestMatch(searchRequest = requestDOB.resolve(), dobModifiedIdentityRecord, entity = false)
          val expected = BestKYCMatch(cluster = dobModifiedIdentityRecord.attributes.head,
            piiMatchResults = Map(FirstNameMatch->0, SurNameMatch->1, SSNMatch-> 1,
              DOBMatch->1,MobileNumberMatch->1,StreetAddressMatch-> 1,
              CityMatch-> 1, ZipCodeMatch->1, StateMatch->1, EmailMatch -> 0), bestMatchedElements = scala.collection.concurrent.TrieMap("surName" -> "paul", "ssn" -> "796040440", "city" -> "youngville", "mobileNumber" -> "**********", "state" -> "la", "zipCode" -> "72100", "dob" -> esDOBString, "streetAddress" -> "602 ne bush prairie private se unit 12345", "middleName" -> "N", "firstName" -> "ashley"), hasFirstNamePartialMatch = Some(false), hasSurNamePartialMatch = Some(true))
          matchedRes match {
            case Right(matchSuccess) =>
              matchSuccess shouldBe expected
            case Left(exception) =>
              fail(exception)
          }
        }
      }
    }

    "should not throw error on bad DOB with radius" in {
      val requestDOB = request.copy(dob = Some(DOB("00000000")), preferencesKyc = KYCPreferences(exactDob = false, dobMatchLogic = Option(DobMatchLogic.FUZZY_MATCH.toString), exactSSN = true))
      val withoutDOBBestMatch = BestKYCMatch(cluster = ElasticSearchIdentityRecord.attributes.head, piiMatchResults = Map(FirstNameMatch->1,  SurNameMatch->1, SSNMatch-> 1,
        DOBMatch->0,MobileNumberMatch->1,StreetAddressMatch-> 1,
        CityMatch-> 1, ZipCodeMatch->1, StateMatch->1, EmailMatch -> 0), bestMatchedElements = scala.collection.concurrent.TrieMap("surName" -> "paul", "ssn" -> "796040440", "city" -> "youngville", "mobileNumber" -> "**********", "state" -> "la", "zipCode" -> "72100", "dob" -> "20030208", "streetAddress" -> "602 ne bush prairie private se unit 12345", "middleName" -> "N", "firstName" -> "ashley"), hasFirstNamePartialMatch = Some(true), hasSurNamePartialMatch = Some(true))
      val matchedRes = resultMatcher.findBestMatch(searchRequest = requestDOB.resolve(), ElasticSearchIdentityRecord, entity = false)
      matchedRes match {
        case Right(matchSuccess) =>
          matchSuccess shouldBe withoutDOBBestMatch
        case Left(exception) =>
          fail(exception)
      }
    }

    "should not match SSN when set to exact" in {
      val withoutSSNBestMatch = BestKYCMatch(cluster = ElasticSearchIdentityRecord.attributes.head,
        piiMatchResults = Map(FirstNameMatch->1, SurNameMatch->1, SSNMatch-> 0, SSN4Match -> 0,
        DOBMatch->1,MobileNumberMatch->1,StreetAddressMatch-> 1,
        CityMatch-> 1, ZipCodeMatch->1, StateMatch->1, EmailMatch -> 0), bestMatchedElements = scala.collection.concurrent.TrieMap("surName" -> "paul", "ssn" -> "796040440", "city" -> "youngville", "mobileNumber" -> "**********", "state" -> "la", "zipCode" -> "72100", "dob" -> "20030208", "streetAddress" -> "602 ne bush prairie private se unit 12345", "middleName" -> "N", "firstName" -> "ashley"), hasFirstNamePartialMatch = Some(true), hasSurNamePartialMatch = Some(true))

      val requestSSN = request.copy(nationalId = Some(NationalId("796040120")))
      val matchedRes = resultMatcher.findBestMatch(searchRequest = requestSSN.resolve(), ElasticSearchIdentityRecord, entity = false)
      matchedRes match {
        case Right(matchSuccess) =>
          matchSuccess shouldBe withoutSSNBestMatch
        case Left(exception) =>
          fail(exception)
      }
    }

    "should fuzzy match SSN is not exact" in {
      val requestSSN = request.copy(nationalId = Some(NationalId("796040120")), preferencesKyc = KYCPreferences(exactDob = true, dobMatchLogic = Option(DobMatchLogic.EXACT_MATCH.toString), exactSSN = false))
      val matchedRes = resultMatcher.findBestMatch(searchRequest = requestSSN.resolve(), ElasticSearchIdentityRecord, entity = false)
      matchedRes match {
        case Right(matchSuccess) =>
          matchSuccess shouldBe bestMatch.copy(piiMatchResults = bestMatch.piiMatchResults ++ Map(PiiAttribute.SSNMatch -> 0, SSN4Match->0))
        case Left(exception) =>
          fail(exception)
      }
    }

    "should not fuzzy match SSN when only 4 characters" in {
      val withoutSSNBestMatch = BestKYCMatch(cluster = ElasticSearchIdentityRecord.attributes.head, piiMatchResults = Map(FirstNameMatch->1, SurNameMatch->1, SSNMatch-> 0,
        DOBMatch->1,MobileNumberMatch->1,StreetAddressMatch-> 1,
        CityMatch-> 1, ZipCodeMatch->1, StateMatch->1, EmailMatch -> 0, SSN4Match->0
      ), bestMatchedElements = scala.collection.concurrent.TrieMap("surName" -> "paul", "ssn" -> "796040440", "city" -> "youngville", "mobileNumber" -> "**********", "state" -> "la", "zipCode" -> "72100", "dob" -> "20030208", "streetAddress" -> "602 ne bush prairie private se unit 12345", "middleName" -> "N", "firstName" -> "ashley"), hasFirstNamePartialMatch = Some(true), hasSurNamePartialMatch = Some(true))

      val requestSSN = request.copy(nationalId = Some(NationalId("0120")), preferencesKyc = KYCPreferences(exactDob = true, dobMatchLogic = Option(DobMatchLogic.EXACT_MATCH.toString), exactSSN = false))
      val matchedRes = resultMatcher.findBestMatch(searchRequest = requestSSN.resolve(), ElasticSearchIdentityRecord, entity = false)
      matchedRes match {
        case Right(matchSuccess) =>
          matchSuccess shouldBe withoutSSNBestMatch
        case Left(exception) =>
          fail(exception)
      }
    }

    "valid SSN: should be true when only serial number" in {
      validateNationalId(NationalId("1234"), NationalId("1234")) shouldBe true
    }
    "valid SSN: should be true when only serial number (left filled)" in {
      validateNationalId(NationalId("000001234"), NationalId("000001234")) shouldBe false // updated as per IDPLUS-4928
    }
    "invalid SSN: should be true when serial number is zeros" in {
      validateNationalId(NationalId("0000"), NationalId("0000")) shouldBe false
    }
    "invalid SSN: should be false when greater than 9 digits" in {
      validateNationalId(NationalId("1234567899"), NationalId("1234567899")) shouldBe false
    }
    "invalid SSN: should be false when area number is zeros" in {
      validateNationalId(NationalId("000456789"), NationalId("000456789")) shouldBe false
    }
    "valid SSN: should be true when first two area numbers are zero" in {
      validateNationalId(NationalId("003456789"), NationalId("003456789")) shouldBe true
    }
    "invalid SSN: should be false when group number is zeros" in {
      validateNationalId(NationalId("123006789"), NationalId("123006789")) shouldBe false
    }
    "valid SSN: should be true when first group number is zero" in {
      validateNationalId(NationalId("123056789"), NationalId("123056789")) shouldBe true
    }
    "valid SSN: should be true when second group number is zero" in {
      validateNationalId(NationalId("123406789"), NationalId("123406789")) shouldBe true
    }
    "invalid SSN: should be false when area number is sixes" in {
      validateNationalId(NationalId("666406789"), NationalId("666406789")) shouldBe false
    }
    "invalid SSN: should be false when widely misused ssn is passed" in {
      validateNationalId(NationalId("078051120"), NationalId("078051120")) shouldBe false
      validateNationalId(NationalId("219099999"), NationalId("219099999")) shouldBe false
    }

    "Valid DOB: Match year only" in {
      matchDobYearOnly(DOB("20030208"), DOB("20030808")) shouldBe true
    }
    "Valid DOB: Match year only one year after" in {
      matchDobYearOnly(DOB("20030208"), DOB("20040808")) shouldBe false
    }
    "Valid DOB: Match year only one year before" in {
      matchDobYearOnly(DOB("20030208"), DOB("20040808")) shouldBe false
    }
    "Valid DOB: Match year only two years after" in {
      matchDobYearOnly(DOB("20030208"), DOB("20060808")) shouldBe false
    }
    "Invalid DOB: Match year only" in {
      matchDobYearOnly(DOB("20030208"), DOB("0060808")) shouldBe false
    }

    "Valid DOB:Fuzzy Match year only" in {
      matchDobYearOnly(DOB("20030208"), DOB("20030808"), Some(true), 1) shouldBe true
    }
    "Valid DOB:Fuzzy Match year only one year after" in {
      matchDobYearOnly(DOB("20030208"), DOB("20040808"), Some(true), 1) shouldBe true
    }
    "Valid DOB:Fuzzy Match year only one year before" in {
      matchDobYearOnly(DOB("20030208"), DOB("20040808"), Some(true), 1) shouldBe true
    }
    "Valid DOB:Fuzzy Match year only two years after" in {
      matchDobYearOnly(DOB("20030208"), DOB("20060808"), Some(true), 1) shouldBe false
    }
    "Invalid DOB:Fuzzy Match year only" in {
      matchDobYearOnly(DOB("20030208"), DOB("0060808"), Some(true), 1) shouldBe false
    }

    "Valid DOB: EXACT_YYYY_SWAPPED_MM_DD_MATCH: Both same DOB" in {
      matchDobYearOnlyMonthDaySwap(DOB("20030208"), DOB("20030208")) shouldBe true
    }
    "Valid DOB: EXACT_YYYY_SWAPPED_MM_DD_MATCH: Exact YYYY, swapped MM,DD" in {
      matchDobYearOnlyMonthDaySwap(DOB("20030208"), DOB("20030802")) shouldBe true
    }
    "Valid DOB: EXACT_YYYY_SWAPPED_MM_DD_MATCH: Exact YYYY, wrong MM,DD" in {
      matchDobYearOnlyMonthDaySwap(DOB("20030208"), DOB("20030808")) shouldBe false
    }
    "Valid DOB: EXACT_YYYY_SWAPPED_MM_DD_MATCH: Wrong YYYY, swapped MM,DD" in {
      matchDobYearOnlyMonthDaySwap(DOB("20030208"), DOB("20040802")) shouldBe false
    }
    "Invalid DOB: EXACT_YYYY_SWAPPED_MM_DD_MATCH" in {
      matchDobYearOnlyMonthDaySwap(DOB("20030208"), DOB("0060808")) shouldBe false
    }

    "Synonym names" - {
      "synonym first name should match" in {
        val (input, esName) = ("nellie", "elaine")
        matchN(input, esName) shouldBe false
        fuzzyNameMatchWithSynonymCheck(input, esName) shouldBe true
      }

      "synonym first name should match 2" in {
        val (input, esName) = ("midge", "maegan")
        matchN(input, esName) shouldBe false
        fuzzyNameMatchWithSynonymCheck(input, esName) shouldBe true
      }

      "synonym surname should match" in {
        val (input, esName) = (SurName("ginger"), SurName("virginia"))
        matchNL(input, esName) shouldBe false
        fuzzyNameMatchWithSynonymCheckL(input, esName) shouldBe true
      }

      "synonym surname should not match" in {
        val (input, esName) = (SurName("ursula"), SurName("charlotte"))
        matchNL(input, esName) shouldBe false
        fuzzyNameMatchWithSynonymCheckL(input, esName) shouldBe false
      }
    }

    "kyc leading zero tests" - {
      val testData = new CSVReaderHeaderAware(new InputStreamReader(getClass.getResourceAsStream("/kyc_leadingzero_tests.csv")))
      Stream
        .continually(testData.readMap())
        .takeWhile(_ != null)
        .foreach { row =>
          val originalId = NationalId(row.get("ssn_uncleaned"))
          val cleanedId = NationalId(row.get("ssn_cleaned"))
          s"for originalId=$originalId and cleanedId=$cleanedId" in {
            val expectedValidationResult = row.get("validateNationalId").toBoolean
            val actualValidationResult = validateNationalId(originalId = originalId, cleanedId)
            actualValidationResult shouldBe expectedValidationResult
          }
        }
    }

    "should match bestMatchedElements" in {
      val matchedRes = resultMatcher.findBestMatch(searchRequest = request.resolve(), ElasticSearchIdentityRecord, entity = false)
      val expected = scala.collection.concurrent.TrieMap("surName" -> "paul", "ssn" -> "796040440", "city" -> "youngville", "mobileNumber" -> "**********", "state" -> "la", "zipCode" -> "72100", "dob" -> "20030208", "streetAddress" -> "602 ne bush prairie private se unit 12345", "middleName" -> "N", "firstName" -> "ashley")
      matchedRes match {
        case Right(matchSuccess) =>
          matchSuccess.bestMatchedElements shouldBe expected
        case Left(exception) =>
          fail(exception)
      }
    }

    "bestMatchedElements should pick head option when there is no match" in {
      val requestUpadted = request.copy(surName = SurName("doe"))
      val matchedRes = resultMatcher.findBestMatch(searchRequest = requestUpadted.resolve(), ElasticSearchIdentityRecord, entity = false)
      val expected = scala.collection.concurrent.TrieMap("surName" -> "paul", "ssn" -> "796040440", "city" -> "youngville", "mobileNumber" -> "**********", "state" -> "la", "zipCode" -> "72100", "dob" -> "20030208", "streetAddress" -> "602 ne bush prairie private se unit 12345", "middleName" -> "N", "firstName" -> "ashley")
      matchedRes match {
        case Right(matchSuccess) =>
          matchSuccess.bestMatchedElements shouldBe expected
        case Left(exception) =>
          fail(exception)
      }
    }

    "bestMatchedElements should match for custom name matching" in {
      val requestUpadted = request.copy(customPreferencesKyc = CustomKYCPreferences(
                                                                  customDobMatchLogic = None,
                                                                  maxAddressCount = None,
                                                                  customNameMatching = Some("custom_name_matching_1001"),
                                                                  maxEmailCount = None,
                                                                  maxPhoneCount = None))

      val matchedRes = resultMatcher.findBestMatch(searchRequest = requestUpadted.resolve(), ElasticSearchIdentityRecord, entity = false)
      val expected = scala.collection.concurrent.TrieMap("suffixName" -> "", "surName" -> "paul", "ssn" -> "796040440", "city" -> "youngville", "mobileNumber" -> "**********", "state" -> "la", "zipCode" -> "72100", "dob" -> "20030208", "streetAddress" -> "602 ne bush prairie private se unit 12345", "middleName" -> "N", "firstName" -> "ashley")
      matchedRes match {
        case Right(matchSuccess) =>
          matchSuccess.bestMatchedElements shouldBe expected
        case Left(exception) =>
          fail(exception)
      }
    }

    "bestMatchedElements should pick head option when there is not match for custom name matching" in {
      val requestUpadted = request.copy(surName = SurName("doe"),
        customPreferencesKyc = CustomKYCPreferences(
        customDobMatchLogic = None,
        maxAddressCount = None,
        customNameMatching = Some("custom_name_matching_1001"),
        maxEmailCount = None,
        maxPhoneCount = None))
      val matchedRes = resultMatcher.findBestMatch(searchRequest = requestUpadted.resolve(), ElasticSearchIdentityRecord, entity = false)
      val expected = scala.collection.concurrent.TrieMap("suffixName" -> "", "surName" -> "paul", "ssn" -> "796040440", "city" -> "youngville", "mobileNumber" -> "**********", "state" -> "la", "zipCode" -> "72100", "dob" -> "20030208", "streetAddress" -> "602 ne bush prairie private se unit 12345", "middleName" -> "N", "firstName" -> "ashley")
      matchedRes match {
        case Right(matchSuccess) =>
          matchSuccess.bestMatchedElements shouldBe expected
        case Left(exception) =>
          fail(exception)
      }
    }

  }

  "should match cashapp name match logic" in {
    Seq(
      ("James","Burless",Array("james"),Array("m"),Array("burless"),Array("sr"),"N"), //match full name
      ("Tomas","Horton",Array("james"),Array("m"),Array("burless"),Array("sr"),""), // no match
      ("M","Horton",Array("james"),Array("m"),Array("burless"),Array("sr"),"NF"), // match firstname-middlename
      ("Burless","James",Array("james"),Array("m"),Array("burless"),Array("sr"),"N"), // match firstname-lastname swap
      ("Tomas","Sr",Array("james"),Array("m"),Array("burless"),Array("sr"),"NL"), //match lastname-suffixname
      ("M","Sr",Array("james"),Array("m"),Array("burless"),Array("sr"),"N"), // match firstname-middlename and surname-suffixname

      // test case where suffix name is empty
      ("James","Burless",Array("james"),Array("m"),Array("burless"),Array(""),"N"), //match full name
      ("Tomas","Horton",Array("james"),Array("m"),Array("burless"),Array(""),""), // no match
      ("M","Horton",Array("james"),Array("m"),Array("burless"),Array(""),"NF"), // match firstname-middlename
      ("Burless","James",Array("james"),Array("m"),Array("burless"),Array(""),"N"), // match firstname-lastname swap
      ("Tomas","Sr",Array("james"),Array("m"),Array("burless"),Array(""),""), //match lastname-suffixname
      ("M","Sr",Array("james"),Array("m"),Array("burless"),Array(""),"NF") // match firstname-middlename and surname-suffixname
    ).foreach{
      case (inputFirstName,inputSurName, fnVendorValues,mnVendorValues,lnVendorValues,nsVendorValues, expected) =>
        NameMatchers.customMatchN1001(inputFirstName, inputSurName, fnVendorValues, mnVendorValues, lnVendorValues, nsVendorValues, false) shouldBe expected
    }
  }

  "should match cashapp name match logic - with additionalCriteriaEnabled" in {
    Seq(
      ("Jr","J",Array("Edward"),Array("J"),Array("Brooks"),Array("Jr"),"N"), //match firstname-suffix and surname-middlename

      ("Edward","J",Array("Edward"),Array("J"),Array("Brooks"),Array("Jr"),"NF"), //Middle name is not considered acceptable match for surName unless both are also shown as flipped
      ("J","Edward",Array("Edward"),Array("J"),Array("Brooks"),Array("Jr"),"NF"), //First name is not considered acceptable match for surName unless it's also shown as flipped
      ("Brooks","Edward",Array("Edward"),Array("J"),Array("Brooks"),Array("Jr"),"N"), //surName and firstName match and are flipped
      ("Edward","Brooks",Array("Edward"),Array("J"),Array("Brooks"),Array("Jr"),"N"), //firstName and surName match properly
      ("J","Brooks",Array("Edward"),Array("J"),Array("Brooks"),Array("Jr"),"N"), //Middle name accepted for firstName and surName also matches
      ("Brooks","J",Array("Edward"),Array("J"),Array("Brooks"),Array("Jr"),"N"), //Middle name accepted for firstName and surName also matches and are shown as flipped
      ("Jr","Brooks",Array("Edward"),Array("J"),Array("Brooks"),Array("Jr"),"NL"), //Suffix is not considered a firstName match when surName is present and matched
      ("Brooks","Jr",Array("Edward"),Array("J"),Array("Brooks"),Array("Jr"),"NL"), //Suffix is not considered a firstName match when surName is present and matched in flipped position
      ("J","Jr",Array("Edward"),Array("J"),Array("Brooks"),Array("Jr"),"N"), //Middle name accepted for firstName and suffix is accepted for surName
      ("Jr","J",Array("Edward"),Array("J"),Array("Brooks"),Array("Jr"),"N"), //Middle name accepted for firstName and suffix is accepted for surName and are shown as flipped
      ("Jr","Edward",Array("Edward"),Array("J"),Array("Brooks"),Array("Jr"),"N"), //firstName matches and suffix is accepted for surName and are shown as flipped
      ("Edward","Jr",Array("Edward"),Array("J"),Array("Brooks"),Array("Jr"),"N"), //firstName and suffix is accepted for surName match properly
      ("Edward","J",Array("Edward"),Array("J"),Array("Brooks"),Array("Jr"),"NF"), //firstName matches but middlename cannot be used for surName matching when not flipped
      ("Jr","Linn",Array("Edward"),Array("N"),Array("Brooks"),Array("Jr"),""), //nomatch firstname-suffix and surname - no match

      ("Jr","Edward",Array("Edward"),Array("J"),Array("Brooks"),Array("Jr"),"N"), //match firstname-suffix and surname-middlename
      ("Jr","J",Array("Edward"),Array("J"),Array("Brooks"),Array(""),""), //nomatch firstname-suffix and surname-middlename
      ("Jr","Edward",Array("Edward"),Array("N"),Array("Brooks"),Array(""),"") //nomatch firstname-suffix and surname-middlename
    ).foreach{
      case (inputFirstName,inputSurName, fnVendorValues,mnVendorValues,lnVendorValues,nsVendorValues, expected) =>
        val result = NameMatchers.customMatchN1001(inputFirstName, inputSurName, fnVendorValues, mnVendorValues, lnVendorValues, nsVendorValues, true)
        result shouldBe expected
    }
  }
}
