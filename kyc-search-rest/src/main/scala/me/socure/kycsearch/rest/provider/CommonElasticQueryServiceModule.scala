package me.socure.kycsearch.rest.provider

import com.google.inject.{AbstractModule, Provides}
import com.typesafe.config.Config
import me.socure.common.kyc.model.QuerySize
import me.socure.common.kyc.service.IdentityDataDynamoService
import me.socure.dynamic.control.center.v2.service.DynamicControlCenterV2Evaluate
import me.socure.kycsearch.rest.workflow.{DynamicElasticQueryService, ElasticQueryService, QueryService}
import me.socure.search.client.LegacyKycElasticSearchClient
import software.amazon.awssdk.services.dynamodb.DynamoDbClient

import javax.inject.{Named, Singleton}
import scala.concurrent.ExecutionContext
import scala.util.Try

class CommonElasticQueryServiceModule extends AbstractModule {

    @Provides
    @Singleton
    @Named("docVQueryService")
    def getDocVElasticQueryService(config: Config)(implicit ec: ExecutionContext): Option[ElasticQueryService] = {
        if(!isDocVSourceEnabled(config))
            None
        else
            Some(new ElasticQueryService(
                client = new LegacyKycElasticSearchClient(
                    client = new ElasticClientProvider(config.getConfig("docv.datasource")).get(),
                    querySize = QuerySize(config.getInt("docv.datasource.elastic.querySize")),
                    useDynamoSource = false,
                    dynamoService = null,
                    vendor = "docv"
                    ),
                index = config.getString("docv.datasource.elastic.indexName")
                ))
    }

    @Provides
    @Singleton
    @Named("equifaxQueryService")
    def getEquifaxElasticQueryService(config: Config, dynamicControlCenterV2Evaluate: DynamicControlCenterV2Evaluate)(implicit ec: ExecutionContext): QueryService = {
        val elasticQueryService = getElasticQueryService(config, "equifax")
        val alternateElasticQueryService = if (config.hasPath("alternate")) {
            Some(getElasticQueryService(config.getConfig("alternate"), "equifax"))
        } else None
        new DynamicElasticQueryService(
            elasticQueryService = elasticQueryService,
            alternateElasticQueryService = alternateElasticQueryService,
            dynamicControlCenterV2Evaluate = dynamicControlCenterV2Evaluate
        )
    }

    @Provides
    @Singleton
    @Named("enformionQueryService")
    def getEnformionElasticQueryService(config: Config, dynamicControlCenterV2Evaluate: DynamicControlCenterV2Evaluate)(implicit ec: ExecutionContext): QueryService = {
        val elasticQueryService = getElasticQueryService(config.getConfig("enf"), "enformion")
        val alternateElasticQueryService = if (config.hasPath("alternate.enf")) {
            Some(getElasticQueryService(config.getConfig("alternate.enf"), "enformion"))
        } else None
        new DynamicElasticQueryService(
            elasticQueryService = elasticQueryService,
            alternateElasticQueryService = alternateElasticQueryService,
            dynamicControlCenterV2Evaluate = dynamicControlCenterV2Evaluate
        )
    }

    @Provides
    @Singleton
    @Named("boldQueryService")
    def getBoldElasticQueryService(config: Config, dynamicControlCenterV2Evaluate: DynamicControlCenterV2Evaluate)(implicit ec: ExecutionContext): Option[QueryService] = {
        if (!isBoldSourceEnabled(config))
            None
        else {
            val elasticQueryService = getElasticQueryService(config.getConfig("bld"), "bold")
            val alternateElasticQueryService = if (config.hasPath("alternate.bld")) {
                Some(getElasticQueryService(config.getConfig("alternate.bld"), "bold"))
            } else None
            Some(new DynamicElasticQueryService(
                elasticQueryService = elasticQueryService,
                alternateElasticQueryService = alternateElasticQueryService,
                dynamicControlCenterV2Evaluate = dynamicControlCenterV2Evaluate
            ))
        }
    }


    @Provides
    @Singleton
    @Named("obitScrapperElasticQueryService")
    def getObitScrapperElasticQueryService(config: Config, dynamicControlCenterV2Evaluate: DynamicControlCenterV2Evaluate)(implicit ec: ExecutionContext): Option[QueryService] = {
        if (!isObitScrapperSourceEnabled(config))
            None
        else {
            val obitConfig = config.getConfig("obitscrapper")
            val elasticQueryService = getElasticQueryService(obitConfig, "obitscrapper", obitConfig.getInt("elastic.timeoutInMillis"))
            val alternateElasticQueryService = if (config.hasPath("alternate.obitscrapper")) {
                Some(getElasticQueryService(config.getConfig("alternate.obitscrapper"), "obitscrapper"))
            } else None
            Some(new DynamicElasticQueryService(
                elasticQueryService = elasticQueryService,
                alternateElasticQueryService = alternateElasticQueryService,
                dynamicControlCenterV2Evaluate = dynamicControlCenterV2Evaluate
            ))
        }
    }
    private def getElasticQueryService(config: Config, vendor: String, timeout: Int = -1)(implicit ec: ExecutionContext): ElasticQueryService = {
        new ElasticQueryService(
            client = new LegacyKycElasticSearchClient(
                client = new ElasticClientProvider(config).get(),
                querySize = QuerySize(config.getInt("elastic.querySize")),
                timeout = timeout,
                useDynamoSource = config.hasPath("elastic.dynamo.enabled") && config.getBoolean("elastic.dynamo.enabled"),
                dynamoService = if (config.hasPath("elastic.dynamo")) {
                    IdentityDataDynamoService(config.getConfig("elastic"), DynamoDbClient.create())
                } else null,
                vendor = vendor
            ),
            index = config.getString("elastic.indexName")
        )
    }

    def isDocVSourceEnabled(config: Config)(implicit ec: ExecutionContext): Boolean = {
        Try(config.getBoolean("docv.datasource.elastic.enabled")).getOrElse(false)
    }

    def isBoldSourceEnabled(config: Config)(implicit ec: ExecutionContext): Boolean = {
        Try(config.getBoolean("bld.elastic.enabled")).getOrElse(false)
    }

    def isObitScrapperSourceEnabled(config: Config)(implicit ec: ExecutionContext): Boolean = {
        Try(config.getBoolean("obitscrapper.elastic.enabled")).getOrElse(false)
    }
}