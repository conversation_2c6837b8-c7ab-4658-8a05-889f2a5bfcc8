package me.socure.kycsearch.rest.factory

import me.socure.common.kyc.KYCFlagsProvider

import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.time.temporal.TemporalAdjusters
import me.socure.common.kyc.model.{DOB, DobMatcherUtil, KycEntitySearchRequest}
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.kycsearch.matcher.DOBMatchers.cleanBD
import me.socure.kycsearch.matcher.DateConversionFactory
import me.socure.kycsearch.rest.model.KYCWorkflowServiceConfig
import me.socure.service.constants.DobMatchLogic._
import org.apache.commons.lang3.StringUtils

import scala.collection.mutable
import scala.util.Try

object ElasticSearchQueryFactory {
  private val metrics: Metrics = JavaMetricsFactory.get(this.getClass)

  private[factory] val FirstNameKey = "NF"
  private[factory] val LastNameKey = "NL"
  private[factory] val NationalIDKey = "SSN"
  private[factory] val DateOfBirthKey = "BD"
  private[factory] val PhoneNumberKey = "PN"
  private[factory] val EMailKey = "EM"
  private[factory] val StreetAddressKey = "AX"
  private[factory] val CityKey = "AY"
  private[factory] val ZipCodeKey = "AZ"
  private[factory] val StateKey = "AS"
  private[factory] val DODKey = "DOD"
  private[factory] val YYYY_MM_DD_FORMAT_STR = "yyyyMMdd"
  private[factory] val YYYY_MM_DD_FORMAT: DateTimeFormatter = DateTimeFormatter.ofPattern(YYYY_MM_DD_FORMAT_STR)
  private[factory] val EMPTY = ""

  def generateNationalIdQuery(searchRequest: KycEntitySearchRequest): Option[String] = {
    searchRequest.nationalId.map(nationalID => {
      getQueryPii(NationalIDKey, nationalID.value)
    })
  }

  def generateNationalIdQuery(nationalIdOpt: Option[String]): Option[String] = {
    nationalIdOpt.map(nationalId => getQueryPii(NationalIDKey, nationalId))
  }

  private def getQueryPii(piiTag: String, value: String): String = {
    val attribute: String = getIndentityQueryAttributes(piiTag, value)
    val is4DigitNationalId = piiTag == "SSN" && value.slice(0, 5) == "00000"
    val cleanedValue = if (is4DigitNationalId) value.slice(5, 9) else value
    "{\"match\": {\"attributes." + attribute + "\": {\"query\": \"" + cleanedValue.replace("\"", "\\\"") + "\"}}}"
  }


  private def getFilterQueryPii(piiTag: String): String = {
    val attribute: String = getIndentityQueryAttributes(piiTag, "")
    s"""
       |{
       |  "term": {
       |    "attributes.$attribute": ""
       |  }
       |},
       |{
       |  "bool": {
       |    "must_not": {
       |      "exists": {
       |        "field": "attributes.$attribute"
       |      }
       |    }
       |  }
       |}
      """.stripMargin
  }

  private def getIndentityQueryAttributes(piiTag: String, value: String) = {
    val is4DigitNationalId = piiTag == "SSN" && value.slice(0, 5) == "00000"

    val attribute = if (piiTag.slice(0, 1) == "A") "A." + piiTag
    else if (is4DigitNationalId) piiTag + ".metaData.last_four"
    else piiTag + ".cleanedValue"
    attribute
  }

  private def getDateRangeSubQuery(piiTag: String, lteValue: String, gteValue: String, format: String): String = {
    if (KYCFlagsProvider.isNewDateRangeQueryEnabled) {
      val yearLow = Try(lteValue.substring(0, 4).toInt).toOption
      val yearHigh = Try(gteValue.substring(0, 4).toInt).toOption
      val years = (yearLow, yearHigh) match {
        case (Some(low), Some(high)) => Seq.range(Math.min(low, high), Math.max(low, high) + 1).mkString("\"", "\",\"", "\"")
        case _ => ""
      }
      s"""
        {
          "terms": {
            "attributes.$piiTag.metaData.dob_year": [$years]
          }
        }
      """
    } else {
      "{\"range\": {\"attributes." + piiTag + ".cleanedValue" + "\": {\"gte\": \"" + gteValue + "\", \"lte\": \"" + lteValue + "\", \"format\": \"" + format + "\"" + "}}}"
    }
  }

  private def getFormattedDate(date: LocalDate): String = {
    YYYY_MM_DD_FORMAT.format(date)
  }

  private def getQueryBDOnYearOnly(piiTag: String, value: String, fuzziness: Option[Boolean] = None): String = {
    val dob = cleanBD(DOB(value))
    val lteValue = fuzziness match {
      case Some(true) => getFormattedDate(DateConversionFactory.parseBD(dob).plusYears(1).`with`(TemporalAdjusters.lastDayOfYear()))
      case _ => getFormattedDate(DateConversionFactory.parseBD(dob).`with`(TemporalAdjusters.lastDayOfYear()))
    }
    val gteValue = fuzziness match {
      case Some(true) => getFormattedDate(DateConversionFactory.parseBD(dob).minusYears(1).`with`(TemporalAdjusters.firstDayOfYear()))
      case _ => getFormattedDate(DateConversionFactory.parseBD(dob).`with`(TemporalAdjusters.firstDayOfYear()))
    }
    getDateRangeSubQuery(piiTag, lteValue, gteValue, YYYY_MM_DD_FORMAT_STR)
  }

  private def getQueryBDOnYYYYMMOnly(piiTag: String, value: String, fuzziness: Option[Boolean]): String = {
    val dob = cleanBD(DOB(value))
    val lteValue = fuzziness match {
      case Some(true) => getFormattedDate(DateConversionFactory.parseBD(dob).plusYears(1).`with`(TemporalAdjusters.lastDayOfMonth()))
      case _ => getFormattedDate(DateConversionFactory.parseBD(dob).`with`(TemporalAdjusters.lastDayOfMonth()))
    }
    val gteValue = fuzziness match {
      case Some(true) => getFormattedDate(DateConversionFactory.parseBD(dob).minusYears(1).`with`(TemporalAdjusters.firstDayOfMonth()))
      case _ => getFormattedDate(DateConversionFactory.parseBD(dob).`with`(TemporalAdjusters.firstDayOfMonth()))
    }
    getDateRangeSubQuery(piiTag, lteValue, gteValue, YYYY_MM_DD_FORMAT_STR)
  }

  private def getQueryBDOnTwoDigitTransposition(piiTag: String, value: String, isDay: Option[Boolean]): Option[String] = {
    val dob = cleanBD(DOB(value))
    (if(isDay.contains(true)) DateConversionFactory.getTranspositionIntervalOnDay(dob, 1)
    else DateConversionFactory.getTranspositionIntervalOnMonth(dob, 1)) match {
      case (start, end) =>
        val lteValue = getFormattedDate(end)
        val gteValue = getFormattedDate(start)
        Some(getDateRangeSubQuery(piiTag, lteValue, gteValue, YYYY_MM_DD_FORMAT_STR))
      case _ => None
    }
  }

  private def getQueryBDOnTwoDigitTranspositionYear(piiTag: String, value: String): String = {
    val dob = DateConversionFactory.parseBD(cleanBD(DOB(value)))
    val modifiedYear = DateConversionFactory.getTwoDigitTranspositionOfYear(dob.getYear)
    val dobStart = getFormattedDate(dob.`with`(TemporalAdjusters.firstDayOfYear()))
    val dobEnd = getFormattedDate(dob.`with`(TemporalAdjusters.lastDayOfYear()))
    val modifiedStart = getFormattedDate(dob.`with`(TemporalAdjusters.firstDayOfYear()).withYear(modifiedYear))
    val modifiedEnd = getFormattedDate(dob.`with`(TemporalAdjusters.lastDayOfYear()).withYear(modifiedYear))
    getDateRangeSubQuery(piiTag, dobEnd, dobStart, YYYY_MM_DD_FORMAT_STR) +
    (if (modifiedYear <= LocalDate.now.getYear) ", " + getDateRangeSubQuery(piiTag, modifiedEnd, modifiedStart, YYYY_MM_DD_FORMAT_STR) else StringUtils.EMPTY)
  }

  private def searchRequestToKeywordMap(searchRequest: KycEntitySearchRequest): Map[String, Option[String]] = {
    Map(
      FirstNameKey -> Some(searchRequest.firstName.value),
      LastNameKey -> Some(searchRequest.surName.value),
      NationalIDKey -> searchRequest.nationalId.map(_.value),
      DateOfBirthKey -> searchRequest.dob.map(_.value),
      PhoneNumberKey -> searchRequest.mobileNumber.map(_.value),
      EMailKey -> searchRequest.email,
      StreetAddressKey -> searchRequest.streetAddress.map(_.value),
      CityKey -> searchRequest.city.map(_.value),
      ZipCodeKey -> searchRequest.zipCode.map(_.value),
      StateKey -> searchRequest.state.map(_.value)
    )
  }

  def generateSubquery(elements: Seq[String], piiMap: scala.collection.Map[String, String], boostOpt: Option[String]): Option[String] = {
    if(elements.forall(piiMap.contains)) {
      var subQuery = "{\"bool\": {\"must\": ["
      subQuery += elements.flatMap(piiTag => piiMap.get(piiTag)).mkString(", ")
      subQuery += boostOpt.map(boost => s"""], "boost": "$boost"}}""").getOrElse("]}}")
      Some(subQuery)
    } else None
  }

  def generateIdentityQuery(searchRequest: KycEntitySearchRequest, entity: Boolean, kycWorkflowServiceConfig: Option[KYCWorkflowServiceConfig] = None): String = {

    val queryPiis: mutable.Map[String, String] = getIdentityQueryPII(searchRequest, entity)
    var query = "{\"bool\": {\"should\": ["

    lazy val subQueryNC = generateSubquery(Seq("NF", "NL", "SSN"), queryPiis, Some("2"))
    lazy val subQueryNB = generateSubquery(Seq("NF", "NL", "BD"), queryPiis, Some("2"))
    lazy val subQueryNA = generateSubquery(Seq("NF", "NL", "AX"), queryPiis, None)
    lazy val subQueryNA2 = generateSubquery(Seq("NF", "NL", "AY", "AS"), queryPiis, None)
    lazy val subQueryNP = generateSubquery(Seq("NF", "NL", "PN"), queryPiis, None)
    lazy val subQueryEM = generateSubquery(Seq("NF", "NL", "EM"), queryPiis, None)
    lazy val subQueyZIP = generateSubquery(Seq("NF", "NL", "AZ"), queryPiis, None)

    lazy val subQueryCB = generateSubquery(Seq("SSN", "BD"), queryPiis, Some("2"))
    lazy val subQueryA = generateSubquery(Seq("AX", "AY", "AS"), queryPiis, None)
    lazy val subQueyPN = generateSubquery(Seq("PN"), queryPiis, None)
    lazy val subQueySSN = generateSubquery(Seq("SSN"), queryPiis, None)
    lazy val subQueyEM = generateSubquery(Seq("EM"), queryPiis, None)
    lazy val subQueryAZ = generateSubquery(Seq("AX", "AZ"), queryPiis, None)

    lazy val subQueyPNC = generateSubquery(Seq("PN", "SSN"), queryPiis, Some("2"))
    lazy val subQueyPNCZ = generateSubquery(Seq("PN", "SSN", "AZ"), queryPiis, Some("3"))
    lazy val subQueyPNB = generateSubquery(Seq("PN", "BD"), queryPiis, Some("2"))
    lazy val subQueyPNBZ = generateSubquery(Seq("PN", "BD", "AZ"), queryPiis, Some("2"))
    lazy val subQueyPNBZE = generateSubquery(Seq("PN", "BD", "AZ", "EM"), queryPiis, Some("3"))

    val additionalSubQueries = if(kycWorkflowServiceConfig.isDefined && kycWorkflowServiceConfig.get.havePhoneBasedQueries){
      Seq(subQueyPNC, subQueyPNCZ, subQueyPNB, subQueyPNBZ, subQueyPNBZE)
    }
    else
      Seq.empty
    val subQueries = if (searchRequest.isSinglePiiRequest) {
      Seq(subQueryCB, subQueryA, subQueyPN, subQueySSN, subQueyEM, subQueryAZ)
    }
    else {
      Seq(subQueryNC, subQueryNB, subQueryCB, subQueryNA, subQueryNA2, subQueryNP, subQueryEM, subQueryA, subQueyZIP, subQueyEM, subQueyPN)
    } ++ additionalSubQueries

    query += subQueries
      .flatten
      .mkString(", ")
    query += "]}}"
    query
  }


  def generateDeceasedQuery(searchRequest: KycEntitySearchRequest, entity: Boolean): String = {
    val queryPiis: mutable.Map[String, String] = getIdentityQueryPII(searchRequest, entity)
    var query = "{\"bool\": {\"must\": [" + "{\"bool\": {\"should\": ["
    queryPiis += (DODKey -> getFilterQueryPii(DODKey))
    val subQueryNB = generateSubquery(Seq("NF", "NL", "BD"), queryPiis, Some("2"))
    val subQueryNA = generateSubquery(Seq("NF", "NL", "AY", "AS"), queryPiis, None)
    val subQueryNAB = generateSubquery(Seq("NF", "NL", "AY", "AS", "BD"), queryPiis, Some("3"))
    val subQueries = Seq(subQueryNB, subQueryNA, subQueryNAB)
    query += subQueries
      .flatten
      .mkString(", ")
    query += "]}},"
    query += "{\"bool\": {\"must_not\": ["
    query += getFilterQueryPii(DODKey)
    query += "]}}]}}"
    query
  }

  private def getIdentityQueryPII(searchRequest: KycEntitySearchRequest, entity: Boolean) = {
    val preferences = if (entity) searchRequest.preferencesEntity else searchRequest.preferencesKyc

    val keywordMap = searchRequestToKeywordMap(searchRequest)

    val queryPiis = scala.collection.mutable.Map[String, String]()
    Array("NF", "NL").foreach { piiTag =>
      keywordMap(piiTag).foreach(value => {
        queryPiis += (piiTag -> getQueryPii(piiTag, value))
      })
    }

    keywordMap(CityKey).foreach(city => {
      queryPiis += (CityKey -> getQueryPii(CityKey, city))
    })

    keywordMap(StreetAddressKey).foreach(streetAddress => {
      queryPiis += (StreetAddressKey -> getQueryPii(StreetAddressKey, streetAddress))
    })

    Array("SSN", "AS").foreach { piiTag =>
      keywordMap(piiTag).foreach(value => {
        queryPiis += (piiTag -> getQueryPii(piiTag, value))
      })
    }

    Array("BD").foreach { piiTag =>
      keywordMap(piiTag).foreach(value => {
        queryPiis += {
          DobMatcherUtil.getDobMatchingLogic(preferences.exactDob, preferences.dobMatchLogic) match {
            case EXACT_MATCH => piiTag -> getQueryPii(piiTag, value)
            case FUZZY_MATCH => piiTag -> getQueryPii(piiTag, value)
            case EXACT_YYYY_MATCH => piiTag -> getQueryBDOnYearOnly(piiTag, value, Some(false))
            case FUZZY_YYYY_MATCH => piiTag -> getQueryBDOnYearOnly(piiTag, value, Some(true))
            case FUZZY_YYYY_MM_MATCH => piiTag -> getQueryBDOnYYYYMMOnly(piiTag, value, Some(true))
            case EXACT_YYYY_MM_MATCH => piiTag -> getQueryBDOnYYYYMMOnly(piiTag, value, Some(false))
            case FUZZY_TWO_DIGIT_TRANSPOSITION_YYYY_MM_DD_MATCH => piiTag -> getQueryBDOnTwoDigitTransposition(piiTag, value, Some(true)).getOrElse(EMPTY)
            case FUZZY_TWO_DIGIT_TRANSPOSITION_YYYY_MM_MATCH => piiTag -> getQueryBDOnTwoDigitTransposition(piiTag, value, Some(false)).getOrElse(EMPTY)
            case TWO_DIGIT_TRANSPOSITION_YYYY_MATCH => piiTag -> getQueryBDOnTwoDigitTranspositionYear(piiTag, value)
            case EXACT_YYYY_SWAPPED_MM_DD_MATCH => piiTag -> getQueryBDOnYearOnly(piiTag, value, Some(false))
          }
        }
      })
    }

    searchRequest.mobileNumber.foreach(mobileNumber => {
      queryPiis += (PhoneNumberKey -> getQueryPii(PhoneNumberKey, mobileNumber.value))
    })

    keywordMap(EMailKey).foreach(value => {
      queryPiis += (EMailKey -> getQueryPii(EMailKey, value))
    })

    searchRequest.zipCode.foreach(zipCode => {
      queryPiis += (ZipCodeKey -> getQueryPii(ZipCodeKey, zipCode.value))
    })
    queryPiis
  }
}
