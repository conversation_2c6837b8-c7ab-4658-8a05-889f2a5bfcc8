package me.socure.kycsearch.rest.provider

import com.typesafe.config.Config
import me.socure.kycsearch.rest.model.KYCWorkflowServiceConfig

import javax.inject.{Inject, Provider}

class KYCWorkflowServiceConfigProvider @Inject()(config: Config) extends Provider[KYCWorkflowServiceConfig] {

  private def getBooleanOrDefault(path: String, default: Boolean = false): Boolean =
    if (config.hasPath(path)) config.getBoolean(path) else default
  override def get(): KYCWorkflowServiceConfig = {
    {
      KYCWorkflowServiceConfig(
        showTop20obitRecords = getBooleanOrDefault("show.top20obitRecords"),
        showNationalIdQueryResults = getBooleanOrDefault("show.nationalIdQueryResults"),
        showAdditonalMatches = getBooleanOrDefault("show.additonalMatches"),
        showTop20EquifaxEntities = getBooleanOrDefault("show.top20EquifaxEntities"),
        showTop20EnformionEntities = getBooleanOrDefault("show.top20EnformionEntities"),
        havePhoneBasedQueries = getBooleanOrDefault("additionalESQuery.havePhoneBasedQueries")
      )
    }
  }
}
