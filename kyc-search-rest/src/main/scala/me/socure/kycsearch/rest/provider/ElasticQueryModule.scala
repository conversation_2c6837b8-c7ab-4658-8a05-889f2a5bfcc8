package me.socure.kycsearch.rest.provider

import com.google.inject.{AbstractModule, Provides}
import com.sksamuel.elastic4s.ElasticClient
import com.typesafe.config.Config
import me.socure.common.kyc.service.IdentityDataDynamoService
import me.socure.search.client.{LegacyKycElasticSearchClient, SearchClient}
import software.amazon.awssdk.services.dynamodb.DynamoDbClient

import javax.inject.Named
import scala.concurrent.ExecutionContext

class ElasticQueryModule extends AbstractModule {
  override def configure(): Unit = {
    bind(classOf[ElasticClient]).toProvider(classOf[ElasticClientProvider])
    bind(classOf[SearchClient]).to(classOf[LegacyKycElasticSearchClient])
  }

  @Provides
  @Named("useEfxDynamoSource")
  private def isDynamoSourceEnabled(config: Config): Boolean = {
    config.hasPath("elastic.dynamo.enabled") && config.getBoolean("elastic.dynamo.enabled")
  }

  @Provides
  @Named("efxDynamoService")
  private def getDynamoService(config: Config)(implicit ec: ExecutionContext): IdentityDataDynamoService = {
    if (config.hasPath("elastic.dynamo")) {
      IdentityDataDynamoService(config.getConfig("elastic"), DynamoDbClient.create())
    } else null
  }
}
