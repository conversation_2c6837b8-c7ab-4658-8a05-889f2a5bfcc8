package me.socure.kycsearch.rest.module

import com.amazonaws.auth.{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, AWSCredentialsProvider<PERSON>hain, DefaultAWSCredentialsProviderChain}
import com.google.inject.{AbstractModule, Provides, Singleton}
import com.typesafe.config.Config
import me.socure.aws.opensearch.signing.AWSRequestSigningApacheInterceptor
import me.socure.common.kyc.model.QuerySize
import me.socure.common.kyc.service.IdentityDataDynamoService
import me.socure.kycsearch.matcher.ResultMatcher
import me.socure.kycsearch.rest.internal.entity.data.{InternalEntityDataDao, InternalEntityDataHandler, InternalEntityESConfig}
import me.socure.kycsearch.rest.provider.ElasticClientProvider
import me.socure.kycsearch.rest.workflow.ElasticQueryService
import me.socure.search.client.LegacyKycElasticSearchClient
import org.apache.http.client.config.RequestConfig
import org.apache.http.impl.nio.client.HttpAsyncClientBuilder
import org.apache.http.nio.client.HttpAsyncClient
import software.amazon.awssdk.services.dynamodb.DynamoDbClient

import java.util.concurrent.TimeUnit
import scala.concurrent.ExecutionContext
import scala.concurrent.duration.Duration


class InternalEntityDataModule extends AbstractModule {

  @Provides
  @Singleton
  private def InternalEntityDataDao(config: Config)(implicit ec: ExecutionContext): InternalEntityDataDao = {
    val internalEntityConfig = config.getConfig("internal_entity")
    val dynamoDbTable = internalEntityConfig.getString("dynamo.table")
    val dynamoDbClient = DynamoDbClient.create()
    val region = internalEntityConfig.getString("elastic.region")
    val elasticQueryService = new ElasticQueryService(
      client = new LegacyKycElasticSearchClient(
        client = new ElasticClientProvider(internalEntityConfig).get(),
        querySize = QuerySize(internalEntityConfig.getInt("elastic.querySize")),
        useDynamoSource = true,
        dynamoService = IdentityDataDynamoService(internalEntityConfig, dynamoDbClient),
        vendor = "internal_entity"
      ),
      index = internalEntityConfig.getString("elastic.indexName")
    )
    val httpClient = getHttpClient(region)
    val esConfig = InternalEntityESConfig(
      protocol = internalEntityConfig.getString("elastic.protocol"),
      host = internalEntityConfig.getString("elastic.host"),
      port = internalEntityConfig.getString("elastic.port"),
      index = internalEntityConfig.getString("elastic.indexName"),
      dynamoDBTable = dynamoDbTable
    )
    new InternalEntityDataDao(
      elasticQueryService = elasticQueryService,
      esConfig = esConfig,
      httpClient = httpClient,
      dynamoDbClient = dynamoDbClient
    )
  }

  private def getHttpClient(region: String): HttpAsyncClient = {

    val requestConfig = RequestConfig.custom()
      .setConnectTimeout(3000)
      .setSocketTimeout(3000)
      .build()

    val httpClient = HttpAsyncClientBuilder
      .create()
      .addInterceptorFirst(getAWSRequestSigningApacheInterceptor(region))
      .setDefaultRequestConfig(requestConfig)
      .setMaxConnPerRoute(1000)
      .setMaxConnTotal(1000)
      .build()
    httpClient.start()
    httpClient
  }

  private def getAWSRequestSigningApacheInterceptor(region: String): AWSRequestSigningApacheInterceptor = {
    val signer = new AWS4Signer()
    signer.setServiceName("es")
    signer.setRegionName(region)
    val instanceProfileProvider = DefaultAWSCredentialsProviderChain.getInstance()
    val chainProvider: AWSCredentialsProviderChain = new AWSCredentialsProviderChain(instanceProfileProvider)
    new AWSRequestSigningApacheInterceptor("es", signer, chainProvider)
  }

  @Provides
  private def getInternalEntityDataHandler(internalEntityDataDao: InternalEntityDataDao, resultMatcher: ResultMatcher, config: Config)
                                          (implicit executionContext: ExecutionContext): InternalEntityDataHandler = {
    val timeout = config.getLong("internal_entity.timeout")
    val isEnabled = config.getBoolean("internal_entity.enabled")
    new InternalEntityDataHandler(internalEntityDataDao, resultMatcher, Duration.create(timeout, TimeUnit.MILLISECONDS), isEnabled)
  }
}
