package me.socure.kycsearch.rest.servlet

import com.google.inject.name.Named
import io.swagger.v3.oas.models.OpenAPI
import me.socure.common.clock.Clock
import me.socure.common.hmac.verifier.{AuthenticationSupport, HMACHttpVerifier}
import me.socure.common.http.NonSecuredHttpFactory
import me.socure.common.json4s.serializer.TimestampJodaDateTimeSerializer
import me.socure.common.kyc.model._
import me.socure.common.kyc.model.es.result.IdentityRecord
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.openapi3.scalatra.OpenApiScalatraSupport
import me.socure.common.openapi3.scalatra.model.{JsonRequestBody, JsonResponseBody}
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.common.sqs.fallback.producer.SqsWithFallbackProducer
import me.socure.common.transaction.id.TrxId
import me.socure.control.center.client.ControlCenterServiceClient
import me.socure.dynamic.control.center.v2.service.DynamicControlCenterV2Evaluate
import me.socure.kycsearch.model.EnformionSuppressedAccountConfig
import me.socure.kycsearch.rest.error.ErrorResponses
import me.socure.kycsearch.rest.factory.KYCRequestCleanerFactory
import me.socure.kycsearch.rest.helper.SQSMessagePushHelper._
import FeatureFlags.FeatureFlag
import me.socure.kycsearch.rest.serializer.{KYCSearchRequestSerializer, KycEntitySearchRequestSerializer}
import me.socure.kycsearch.rest.servlet.spec.CommonSpec
import me.socure.kycsearch.rest.workflow.KYCWorkflowService
import me.socure.kycsearch.rulecodes.common.DOBResolverHelper.daysBetweenTwoDate
import me.socure.model.ErrorResponse
import me.socure.service.audit.client.TPAuditClient
import me.socure.service.audit.model.AuditInfo
import me.socure.service.constants.DobMatchLogic
import me.socure.thirdparty.audit.common.ThirdPartyServiceIds
import me.socure.transaction.resolved.entity.common.model.PIIData
import me.socure.transaction.resolved.entity.common.model.queue.{BestMatchEntityQueueData, MessageType, ResolvedEntityQueueMessage}
import org.joda.time.DateTime
import org.json4s.ext.EnumNameSerializer
import org.json4s.jackson.Serialization
import org.json4s.{DefaultFormats, Formats, JValue}
import org.scalatra
import org.scalatra.json.JacksonJsonSupport
import org.scalatra.{AsyncResult, FutureSupport, ScalatraServlet}
import org.slf4j.{Logger, LoggerFactory}

import java.nio.charset.Charset
import javax.inject.Inject
import scala.concurrent.{ExecutionContext, Future}
import scala.reflect.runtime.{universe => ru}
import scala.util.control.NonFatal
import scala.util.{Failure, Success, Try}

class KYCSearchServlet @Inject()(kYCWorkflowService: KYCWorkflowService,
                                 controlCenterClient: ControlCenterServiceClient,
                                 @Named("RealClock") clock: Clock,
                                 tpAuditClient: TPAuditClient,
                                 val hmacVerifier: HMACHttpVerifier,
                                 val openApi: OpenAPI,
                                 implicit val executor : ExecutionContext,
                                 enformionSuppressedAccountsConfig:EnformionSuppressedAccountConfig,
                                 sqsclient : SqsWithFallbackProducer,
                                 @Named("kycServerlessEndpoint") kycServerlessEndpoint: Option[String],
                                 dynamicControlCenterV2Evaluate: DynamicControlCenterV2Evaluate
                                ) extends ScalatraServlet with JacksonJsonSupport with FutureSupport with AuthenticationSupport with OpenApiScalatraSupport {

  override val logger: Logger = LoggerFactory.getLogger(getClass)
  private val metrics: Metrics = JavaMetricsFactory.get(classOf[KYCSearchServlet])

  private val http = new NonSecuredHttpFactory().getHttpClient()

  override protected implicit def jsonFormats: Formats = DefaultFormats + new KYCSearchRequestSerializer + KycEntitySearchRequestSerializer ++ List(new EnumNameSerializer(NameMatchLogic), new EnumNameSerializer(NameMatchPosition), new EnumNameSerializer(NameMatchType), new EnumNameSerializer(DobMatchLogic), new EnumNameSerializer(MessageType), new EnumNameSerializer(SourceVerificationCodes), TimestampJodaDateTimeSerializer())

  val searchRequestSpec: scalatra.RouteTransformer = endpointSpec(
    description = "KYC Search Endpoint",
    summary = "Returns rulecodes and reasoncodes that were calculated for this KYC search request",
    params = Set.empty,
    requestBodies = Some(JsonRequestBody(`type` = ru.typeOf[KycEntitySearchRequest], required = true, description = Some("Search Request"))),
    responseBodies = Some(Map(
      200 -> JsonResponseBody(`type` = ru.typeOf[KycEntityResponse], description = Some("Response containing kyc rulecodes and reasoncodes"))
    ) ++ CommonSpec.errorResponses)
  )

  error {
    case e: Throwable =>
      logger.error("An error occured", e)
      ScalatraResponseFactory.get(Future.successful(Left(ErrorResponse(115, e.getMessage.split("at")(0)))))
  }

  post("/") { // @deprecated to be removed
    process(parsedBody.extract[KYCSearchRequest].toKycEntitySearchRequest(Workflows.Kyc))(res => KYCResponse(res.kyc))
  }

  post("/entity") { // @deprecated to be removed
    process(parsedBody.extract[KYCSearchRequest].toKycEntitySearchRequest(Workflows.Entity))(res => KYCResponse(res.entity))
  }

  post("/kyc-entity") {
    callKycServerlessEndpoint(parsedBody)
    process(parsedBody.extract[KycEntitySearchRequest])(identity)
  }

  private def process[A](kycEntitySearchRequest: => KycEntitySearchRequest)(f: KycEntityResponse => A): AsyncResult = {
    val startTime = clock.now().toDate
    val searchRequest = Try {
      kycEntitySearchRequest
    } match {
      case Success(res) => res
      case Failure(ex) =>
        metrics.increment("Invalid request")
        logger.error("Failed to parse user input", ex)
        throw new Exception("Unable to parse request body", ex)
    }
    val result: Future[Either[ErrorResponse, A]] = KYCRequestCleanerFactory.clean(searchRequest) match {
      case Left(error) => {
        val errorResponse = ErrorResponses.convertErrorToErrorResponse(error)
        Future.failed(errorResponse)
      }
      case Right(cleanedRequest) =>
        val accountId = cleanedRequest.resolvedReq.accountId
        val isEnformionSuppressedAccount = accountId.exists(inputId => enformionSuppressedAccountsConfig.accountIds.contains(inputId))

        fetchFeatureFlags(cleanedRequest.resolvedReq).flatMap {
          featureFlags => metrics.timeFuture("workflow.process.duration") {
            val updatedRequest = cleanedRequest.setAdditionalNameMatchingCheck(true).setFeatureFlags(featureFlags)
            kYCWorkflowService
              .process(updatedRequest, !isEnformionSuppressedAccount)
              .map(_.right.map(f))
          }
        }
    }

    result.onComplete {
      case Success(Right(successResponse)) =>
        val kycEntityResponse = successResponse.asInstanceOf[KycEntityResponse]
        val jsonString = Serialization.write(kycEntityResponse)
        val auditInfo = AuditInfo(
          accountId = searchRequest.accountId.getOrElse(0),
          transactionId = searchRequest.transactionId.getOrElse(""),
          serviceName = ThirdPartyServiceIds.KycSearchServiceResponse.name,
          startTime = startTime,
          processingTime = clock.now().getMillis - startTime.getTime,
          isCache = false,
          isError = false,
          request = "",
          requestBody = "",
          response = jsonString,
          uuid = null)
        tpAuditClient.audit(auditInfo, obfuscatePII = searchRequest.maskPii)(TrxId(searchRequest.transactionId.getOrElse("")))
        pushBestMatchEntityToSQS(
          searchRequest,
          kycEntityResponse,
          searchRequest.accountId.getOrElse(0),
          searchRequest.transactionId.getOrElse(""),
          searchRequest.submissionDate.map(_.value),
          searchRequest.modulesEnabled)
      case Success(Left(errorResponse)) =>
        logger.error(s"kyc failure so not pushing message to SQS. ${errorResponse}")
      case Failure(_) =>
        logger.error("kyc failure so not pushing message to SQS")
    }

    ScalatraResponseFactory.get {
      metrics.timeFuture("search") {
        result.map {
          case Right(r:KycEntityResponse) if (!(searchRequest.showData.isDefined && searchRequest.showData.get)) => {
            Right(r.copy(equifaxBestMatchEntity=None, enformionBestMatchEntity=None, unifiedBestMatchEntity=None, foundInEfx=None, foundInEnf=None, foundInBoth=None, foundInUnified=None, top20UnifiedEntities=None, top20EquifaxEntities=None, top20EnformionEntities=None, errorResponse=None, anchorRecordUsingInputSSN=None, top20obitRecords=None, top10AnchorRecordUsingInputSSN=None, anchorRecordUsingBMESSN=None, top10AnchorRecordUsingBME=None, nationalIdQueryResults=None, top10eCBSVEntities=None, mergedEntity=None, mergedEntityStatus=None, bestMatchedInternalEntity=None, additionalMatches=None))
          }
          case Right(r) => Right(r)
          case Left(e) => Left(e)
        }
      }
    }
  }

  private def fetchFeatureFlags(resolvedReq: KycEntitySearchRequest): Future[Map[FeatureFlag, Boolean]] = {
    val groupedFlags = FeatureFlags.listFlags.groupBy(_.group)

    val featureFlagsFutures = groupedFlags.map { case (group, flags) =>
      val flagNames = flags.map(_.name).toSet
      val context = Some("accountId" -> resolvedReq.accountId.toString)

      dynamicControlCenterV2Evaluate
        .evaluateForMultipleFlags(group, flagNames, None, context)
        .map(_.flatMap {
          case (flagName, Right(response)) => FeatureFlags.byName(flagName).map( _ -> response.isFlagActive)
          case (flagName, Left(error)) =>
            logger.error(s"Failed to check status for feature flag - $flagName - $error")
            FeatureFlags.byName(flagName).map( _ -> false)
        })
    }

    Future.sequence(featureFlagsFutures).map(_.reduce((a,b) => a ++ b))
  }

  private val sqsServiceName: String = "kyc"

  private def pushBestMatchEntityToSQS(inputRequest: KycEntitySearchRequest, kycresponse: KycEntityResponse, accountId: Long, transactionId: String, transactionTimestamp: Option[String], modulesEnabled: Set[String]): Unit = {

    // This code will be removed after product analysis
    if(evaluateBmeReturnCriteria(kycresponse)) {
      val vendor = findVendorFromClusterID(kycresponse.kycRawBestMatchEntityForPreFill.getOrElse(BestMatchEntity()).clusterId)
      val bmeDOBs = if(vendor == "equifax") kycresponse.top20EquifaxEntities.getOrElse(List.empty).headOption.getOrElse(IdentityRecord.empty).dob
      else kycresponse.top20EnformionEntities.getOrElse(List.empty).headOption.getOrElse(IdentityRecord.empty).dob
      pushDOBmetrics(bmeDOBs.map(_.value).toArray, vendor)
      // End of code to be removed

      val txnTimestamp = if(transactionTimestamp.nonEmpty) new DateTime(transactionTimestamp.get)
      else DateTime.now
      if(modulesEnabled.map(_.toLowerCase).contains("kyc") && inputRequest.customPreferencesKyc.isDisplayBMEEnabled.getOrElse(false)) {
        val bestMatchEntity = kycresponse.kycRawBestMatchEntityForPreFill.getOrElse(BestMatchEntity())
        val resolvedEntityQueueMessage =
          ResolvedEntityQueueMessage(
            messageType = MessageType.BestMatchEntity,
            data = BestMatchEntityQueueData(
              accountId = accountId,
              transactionId = transactionId,
              transactionTimestamp = txnTimestamp,
              sourceVendor = findVendorFromClusterID(bestMatchEntity.clusterId),
              sourceService = sqsServiceName,
              piiData = PIIData(
                firstName = bestMatchEntity.firstName,
                middleName = bestMatchEntity.middleName,
                surName = bestMatchEntity.surName,
                suffixName = bestMatchEntity.suffixName,
                ssn = bestMatchEntity.ssn,
                ssnIssued = bestMatchEntity.ssnIssued,
                dob = bestMatchEntity.dob,
                mobileNumber = bestMatchEntity.mobileNumber,
                emailAddress = bestMatchEntity.emailAddress,

                //Address components like streetAddress, city, state, zipCode should have normalized input address and not BME details
                streetAddress = inputRequest.streetAddress.map(_.value),
                city = inputRequest.city.map(_.value),
                zipCode = inputRequest.zipCode.map(_.value),
                state = inputRequest.state.map(_.value),

                ssnDeceased = bestMatchEntity.ssnDeceased,
                cidDeceased = bestMatchEntity.cidDeceased,
                clusterId = bestMatchEntity.clusterId,
                associatedAddresses = getAssociatedAddresses(bestMatchEntity.associatedAddresses),
                associatedPhoneNumbers = getAssociatedPhoneNumbers(bestMatchEntity.associatedPhoneNumbers),
                associatedEmails = getAssociatedEmails(bestMatchEntity.associatedEmails),
                deceasedDate = bestMatchEntity.deceasedDate
                )
              )
            )
        sqsclient.send(referenceId = transactionId, message = Serialization.write(resolvedEntityQueueMessage), s3FileName = Some(transactionId + ".txt")).onComplete {
          case Success(s) => {
            logger.debug("sqs push success")
          }
          case Failure(ex) => {
            logger.error(s"failed to push to sqs $ex")
          }
        }
      }
    }
  }

  private def pushDOBmetrics(dobs : Array[String], vendor: String): Unit = {
    if(dobs.nonEmpty && dobs.length > 1) {
      val dobCount = dobs.length
      val maxDayDiff = dobs.tail.foldLeft(0L)((maxDiff, element) => {
        daysBetweenTwoDate(dobs(0), element).max(maxDiff)
      })
      metrics.increment("kyc.bme.dob.count", "dobcount:"+dobCount, "tag:"+vendor)
      metrics.gauge("kyc.bme.dob.diff.days",maxDayDiff, "tag:"+vendor)
    }
  }

  private def callKycServerlessEndpoint(requestObj: JValue): Unit = {
    if (kycServerlessEndpoint.isDefined) {
      Future {
        val requestBody = Serialization.write(requestObj)
        logger.debug(requestBody)
        dispatch.url(s"${kycServerlessEndpoint.get}/search/kyc-entity")
          .setHeader("Accept", "application/json")
          .setHeader("Content-Type", "application/json")
          .setBodyEncoding(Charset.forName("UTF-8")) << requestBody
      }.flatMap { request =>
        http(request).map { res =>
          logger.debug(s"KYC Serverless API status ${res.getStatusCode}")
        }
      }.onFailure {
        case NonFatal(ex) => logger.error(s"KYC Serverless API failed", ex)
      }
    }
  }
}

