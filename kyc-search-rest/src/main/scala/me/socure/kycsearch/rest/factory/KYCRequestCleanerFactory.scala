package me.socure.kycsearch.rest.factory

import me.socure.common.kyc.model.{KYCSearchRequestResolved, KycEntitySearchRequest, KycProcessingMetadata, ValidationErrors}
import me.socure.kycsearch.model.NationalIdResolver
import me.socure.kycsearch.rest.data.cleaning._
import me.socure.common.kyc.model.FeatureFlags.FeatureFlag
import org.slf4j.{Logger, LoggerFactory}

import scala.util.{Failure, Success, Try}

object KYCRequestCleanerFactory {
  val logger: Logger = LoggerFactory.getLogger(getClass)


  private def computeProcessingMetaData(request: KycEntitySearchRequest, is4DigitSsn: Boolean): KycProcessingMetadata = {

    var inputPiiCount: Int = 0
    val isQualifiedAddressProvided: Boolean = {
      (request.streetAddress, request.city, request.state, request.zipCode) match {
        case (Some(_), Some(_), Some(_), _) => true
        case (Some(_), _, _, Some(_)) => true
        case _ => false
      }
    }

    if(request.firstName.value.nonEmpty || request.surName.value.nonEmpty) inputPiiCount += 1
    if(isQualifiedAddressProvided) inputPiiCount += 1
    if (request.dob.nonEmpty) inputPiiCount += 1
    if (request.nationalId.nonEmpty) inputPiiCount += 1
    if (request.email.nonEmpty) inputPiiCount += 1
    if (request.mobileNumber.nonEmpty) inputPiiCount += 1


    KycProcessingMetadata(
      inputPiiCount = inputPiiCount,
      isNotMoreThan2Pii = inputPiiCount <= 2,
      is4DigitSsn = is4DigitSsn
    )
  }

  def clean(searchRequest: KycEntitySearchRequest): Either[String, KYCSearchRequestResolved] = {
    Try {
      for {
        firstName <- FirstNameCleaner.clean(searchRequest.firstName).right
        middleName <- MiddleNameCleaner.clean(searchRequest.middleName).right
        surName <- SurNameCleaner.clean(searchRequest.surName).right
        nationalId <- KindNationalIdCleaner.clean(searchRequest.nationalId).right
        streetAddress <- KindStreetAddressCleaner.clean(searchRequest.streetAddress).right
        city <- KindCityCleaner.clean(searchRequest.city).right
        state <- KindStateCleaner.clean(searchRequest.state).right
        zip <- KindZipCodeCleaner.clean(searchRequest.zipCode).right
        dob <- KindDOBCleaner.clean(searchRequest.dob).right
        mobileNumber <- KindMobileNumberCleaner.clean(searchRequest.mobileNumber).right
        email <- KindEMailIdCleaner.clean(searchRequest.email).right
        _ <- (if(searchRequest.workflows.isEmpty) Left("No workflow provided.") else Right()).right
      } yield {
        val resolved = searchRequest.copy(
          firstName = firstName,
          middleName = middleName,
          surName = surName,
          nationalId = nationalId,
          streetAddress = streetAddress,
          city = city,
          zipCode = zip,
          zip4 = None,
          latitude = None,
          longitude = None,
          dob = dob,
          mobileNumber = mobileNumber,
          email = email,
          state = state
        )
        val nationalIdResolved = (searchRequest.nationalId, nationalId) match {
          case (Some(orig), Some(resolved)) => Some(NationalIdResolver.resolveNationalId(
            orig = orig,
            resolved = resolved
          ))
          case _ => None
        }
        val supportedSinglePiiModules = Set("kyc","kycplus","prefill")
        if (searchRequest.isSinglePiiRequest && searchRequest.modulesEnabled.map(_.toLowerCase).intersect(supportedSinglePiiModules).isEmpty) {
          throw new Exception(ValidationErrors.MissingRequiredParameters.toString)
        }
        val processingMetadata = computeProcessingMetaData(searchRequest, resolved.nationalId.map(_.value).exists(_.substring(0,5).equals("00000")))
        val isValidFullDigitNationalId = nationalIdResolved.exists(_.isValid) && nationalId.exists(id => id.value.length == 9 && id.value.slice(0,5) != "00000")
        if(searchRequest.isSinglePiiRequest && processingMetadata.inputPiiCount == 1 && nationalId.exists(_.value.nonEmpty) && !isValidFullDigitNationalId) {
          throw new Exception(ValidationErrors.MissingRequiredParameters.toString)
        }
        KYCSearchRequestResolved(
          originalReq = searchRequest,
          resolvedReq = resolved,
          nationalIdResolved = nationalIdResolved,
          processingMetadata = processingMetadata
        )
      }
    } match {
      case Success(res) => res
      case Failure(ex) =>
        logger.error("Failed to clean search request", ex)
        Left(ex.getMessage)
    }

  }

}
