package me.socure.kycsearch.rest.utils

import com.google.i18n.phonenumbers.{NumberParseException, PhoneNumberUtil}
import me.socure.common.kyc.model.PiiAttribute.PiiAttribute
import me.socure.common.kyc.model.es.result.Records
import me.socure.common.kyc.model.{AddressObject, City, CustomKYCPreferences, EmailObject, KycEntitySearchRequest, MobileNumber, PhoneNumberObject, PiiAttribute, State, StreetAddress, ZipCode}
import me.socure.kycsearch.matcher.{AddressMatchers, NameMatchers, PhoneNumberMatchers}
import me.socure.kycsearch.model.{BestKYCMatch, KYCMatchingConfig}
import me.socure.kycsearch.rulecodes.common.AddressResolverHelper

import scala.collection.mutable.ListBuffer


object BestMatchEntityPresenterUtils {
  // Correct definition of mobileNumberMatchFunc
  private def mobileNumberMatchFunc(mobile: String, resolvedMobile: Option[MobileNumber]): Boolean = {
    val cleanedMobileNumber: Option[MobileNumber] = PhoneNumberMatchers.cleanPN(MobileNumber(mobile))
    cleanedMobileNumber.exists { cleaned =>
      resolvedMobile.exists(resolved => PhoneNumberMatchers.matchPN(cleaned, resolved))
    }
  }

  // Correct definition of emailMatchFunc
  private def emailMatchFunc(email: String, resolvedEmail: Option[String]): Boolean = {
    resolvedEmail.exists(resolved => resolved == email)
  }

  private def matchIndex[T,K](values: Array[T], resolvedValue: Option[K], matchFunc: (T, Option[K]) => Boolean): Array[Boolean] = {
    values.map(x => matchFunc(x, resolvedValue))
  }


  def getAssociatedPhoneNumberList(bestKYCMatch: BestKYCMatch, customPreferences: CustomKYCPreferences, resolvedReq: KycEntitySearchRequest): Option[List[PhoneNumberObject]] =
  {
    val phoneUtil = PhoneNumberUtil.getInstance()
    val associatedPhoneNumbersList: ListBuffer[PhoneNumberObject] = ListBuffer()
    val phoneList: ListBuffer[PhoneNumberObject] = ListBuffer()

    val mobileNumbers = bestKYCMatch.cluster.mobileNumber
    val mobileNumbersFirstSeen = bestKYCMatch.cluster.phoneFirstSeen
    val mobileNumbersLastSeen = bestKYCMatch.cluster.phoneLastSeen

    val mobileNumberMatchResult: Array[Boolean] = matchIndex(mobileNumbers, resolvedReq.mobileNumber, mobileNumberMatchFunc)

    mobileNumberMatchResult.zipWithIndex.foreach {
      case (matchFound, index) =>
        try {
          val parsedNumber = phoneUtil.parse(mobileNumbers(index), "US")
          val formattedNumber = phoneUtil.format(parsedNumber, PhoneNumberUtil.PhoneNumberFormat.E164)
          val phoneNumberObject: PhoneNumberObject= PhoneNumberObject(
            phoneNumber = formattedNumber,
            firstSeenDate = Some(mobileNumbersFirstSeen(index)),
            lastSeenDate = Some(mobileNumbersLastSeen(index))
          )
          if(matchFound) associatedPhoneNumbersList += phoneNumberObject
          else phoneList += phoneNumberObject
        } catch {
          case _: NumberParseException => None
        }
    }

    customPreferences.maxPhoneCount match {
      case Some(maxPhoneCount) => {
        val maxPhoneCountNew =
          if (associatedPhoneNumbersList.nonEmpty) maxPhoneCount - 1
          else maxPhoneCount
        Option((deduplicateAndSortPhoneNumberObjectList(associatedPhoneNumbersList) ++ deduplicateAndSortPhoneNumberObjectList(phoneList).slice(0, maxPhoneCountNew)).toList)
      }
      case None => None
    }

  }

  /**
   * Gets the associated addresses based on customPreferences.maxAddressCount.
   * The first address of best match is skipped.
   *
   * @param bestKYCMatch
   * @param customPreferences
   * @return
   */
  def getAssociatedAddressList(bestKYCMatch: BestKYCMatch, customPreferences: CustomKYCPreferences, resolvedReq: KycEntitySearchRequest, defaultMatchWeights : KYCMatchingConfig): Option[List[AddressObject]] = {
    val associatedAddressList: ListBuffer[AddressObject] = ListBuffer()
    val addressList: ListBuffer[AddressObject] = ListBuffer()

    val (city, state, zip, street, firstSeenDate, lastSeenDate) = extractClusterDetails(bestKYCMatch.cluster)

    val streetAddressMatchIndexes: Array[Double] = calculateStreetAddressMatchWeights(street, zip, state, city, resolvedReq, defaultMatchWeights)

    val maxMatchWeight = if(streetAddressMatchIndexes.isEmpty) 0.0 else streetAddressMatchIndexes.max

    streetAddressMatchIndexes.zipWithIndex.foreach { case (matchWeight, index) =>
      val address = AddressObject(
        streetAddress = convertToProperCase(street.lift(index).getOrElse("")),
        city = convertToProperCase(city.lift(index).getOrElse("")),
        state = convertToUpperCase(state.lift(index).getOrElse("")),
        zipCode = AddressResolverHelper.formatZipCode(zip.lift(index).getOrElse("")),
        firstSeenDate = Some(firstSeenDate.lift(index).getOrElse("")),
        lastSeenDate = Some(lastSeenDate.lift(index).getOrElse(""))
      )
      val targetList = if (maxMatchWeight == 0) addressList else {
        if (maxMatchWeight == matchWeight && maxMatchWeight >=4.0) associatedAddressList else addressList
      }
      targetList += address
    }

    val dedupedAddresses = deduplicateAndSortAddressList(associatedAddressList) ++ deduplicateAndSortAddressList(addressList)

    customPreferences.maxAddressCount match {
      case Some(maxAddressCount) =>
        Option(dedupedAddresses.take(maxAddressCount))
      case None => None
    }
  }


  private def calculateStreetAddressMatchWeights( streets: Array[String],
                                                  zip: Array[String],
                                                  state: Array[String],
                                                  city: Array[String],
                                                  resolvedReq: KycEntitySearchRequest,
                                                  defaultMatchWeights: KYCMatchingConfig): Array[Double] = {
    streets.zipWithIndex.map { case (streetValue, index) =>
      val cleanedStreetAddress = AddressMatchers.cleanStreetAddress(Some(StreetAddress(streetValue)))
      val zipCode = ZipCode(zip(index))
      val stateValue = state(index)
      val cityValue = city(index)

      def calculateWeight(addWeight: => Boolean)(wright: Double): Double =
        if (addWeight) wright else 0.0

      val streetMatchWeight = calculateWeight(AddressMatchers.matchStreetAddressOptimized(cleanedStreetAddress, resolvedReq.streetAddress, resolvedReq.preferencesKyc.addressMatchLogic, resolvedReq.addressComponents))(defaultMatchWeights.streetAddressWeight)
      val zipMatchWeight = calculateWeight(AddressMatchers.matchZipCode(zipCode, resolvedReq.zipCode.getOrElse(ZipCode.empty)))(defaultMatchWeights.zipWeight)
      val stateMatchWeight = calculateWeight(AddressMatchers.matchAS(stateValue, resolvedReq.state.getOrElse(State("")).value))(defaultMatchWeights.stateWeight)
      val cityMatchWeight = calculateWeight(NameMatchers.matchN(cityValue, resolvedReq.city.getOrElse(City("")).value))(defaultMatchWeights.cityWeight)

      streetMatchWeight + zipMatchWeight + stateMatchWeight + cityMatchWeight
    }
  }

  def getAssociatedEmailList(bestKYCMatch: BestKYCMatch, customPreferences: CustomKYCPreferences, resolvedReq: KycEntitySearchRequest): Option[List[EmailObject]] = {
    val associatedEmailsList: ListBuffer[EmailObject] = ListBuffer()
    val emailsList: ListBuffer[EmailObject] = ListBuffer()
    val emails = bestKYCMatch.cluster.emailAddress
    val emailFirstSeen = bestKYCMatch.cluster.emailFirstSeen
    val emailLastSeen = bestKYCMatch.cluster.emailLastSeen

    val emailMatchResult: Array[Boolean] = matchIndex(emails, resolvedReq.email, emailMatchFunc)

    emailMatchResult.zipWithIndex.foreach {
      case (matchFound, index) =>
        val email =  EmailObject(
          emailAddress = emails(index), firstSeenDate = Some(emailFirstSeen.lift(index).getOrElse("")), lastSeenDate = Some(emailLastSeen.lift(index).getOrElse(""))
        )
        if (matchFound) {
          associatedEmailsList += email
        } else {
          emailsList += email
        }
    }

    customPreferences.maxEmailCount match {
      case Some(maxEmailCount) => {
        val maxEmailCountNew =
          if (associatedEmailsList.nonEmpty) maxEmailCount - 1
          else maxEmailCount
        Option((deduplicateAndSortEmailList(associatedEmailsList) ++ deduplicateAndSortEmailList(emailsList).slice(0, maxEmailCountNew)).toList)
      }
      case None => None
    }
  }

  // Generic function to deduplicate and sort based on the latest date and grouping criterion
  private def deduplicateAndSort[T, K](itemList: ListBuffer[T], groupByFunc: T => K, sortKeyFunc: T => Option[String]): List[T] = {
    itemList
      .groupBy(groupByFunc) // Group by the result of the grouping function
      .map {
        case (_, items) =>
          items.maxBy(item => sortKeyFunc(item).getOrElse(""))
      }
      .toList
      .sortBy(item => sortKeyFunc(item).getOrElse("")) // Sort by the date in ascending order
      .reverse // Reverse to get the latest first
  }

  // Usage for AddressObject
  private def deduplicateAndSortAddressList(addressList: ListBuffer[AddressObject]): List[AddressObject] = {
    deduplicateAndSort(addressList,
      (address: AddressObject) => (address.streetAddress, address.city, address.state, address.zipCode.take(5)),
      (address: AddressObject) => address.lastSeenDate.orElse(address.firstSeenDate).orElse(Option(address.zipCode))) // Group by address fields and extract the date
  }

  // Usage for EmailObject
  private def deduplicateAndSortEmailList(emailList: ListBuffer[EmailObject]): List[EmailObject] = {
    deduplicateAndSort(emailList,
      (email: EmailObject) => email.emailAddress, // Group by email address
      (email: EmailObject) => email.lastSeenDate.orElse(email.firstSeenDate)) // Extract the date from email object
  }

  // Usage for PhoneNumberObject
  private def deduplicateAndSortPhoneNumberObjectList(phoneNumberList: ListBuffer[PhoneNumberObject]): List[PhoneNumberObject] = {
    deduplicateAndSort(phoneNumberList,
      (phoneNumberObject: PhoneNumberObject) => phoneNumberObject.phoneNumber, // Group by phone number
      (phoneNumberObject: PhoneNumberObject) => phoneNumberObject.lastSeenDate.orElse(phoneNumberObject.firstSeenDate)) // Extract the date from phone number object
  }

  private def convertToProperCase(value: Option[String]): Option[String] = value.map(convertToProperCase)
  private def convertToUpperCase(value: Option[String]): Option[String] = value.map(convertToUpperCase)

  private def convertToProperCase(value: String): String = {
    if (value.nonEmpty) {
      value.toLowerCase.split(' ').map(_.capitalize).mkString(" ")
    } else value
  }

  private def convertToUpperCase(value: String): String = {
    if (value.nonEmpty) {
      value.toUpperCase
    } else value
  }

  def extractClusterDetails(cluster: Records): (Array[String], Array[String], Array[String], Array[String], Array[String], Array[String]) = {
    (
      cluster.city,
      cluster.state,
      cluster.zipCode,
      cluster.streetAddress,
      cluster.addressFirstSeen,
      cluster.addressLastSeen
    )
  }

  def validatePrefillStatus(request: KycEntitySearchRequest, piiMatchResults: Map[PiiAttribute, Int]): Boolean = {
    def hasMatch(piiAttribute: PiiAttribute): Boolean = piiMatchResults.get(piiAttribute).contains(1)
    def isAddressProvided: Boolean = {
      request.streetAddress.exists(_.value.nonEmpty) && (
        request.zipCode.exists(_.value.nonEmpty) || (request.city.exists(_.value.nonEmpty) && request.state.exists(_.value.nonEmpty))
      )
    }

    val piiMatches = Seq(
      (request.firstName.value.nonEmpty && request.surName.value.nonEmpty, hasMatch(PiiAttribute.FirstNameMatch) && hasMatch(PiiAttribute.SurNameMatch)),
      (request.dob.exists(_.value.nonEmpty), hasMatch(PiiAttribute.DOBMatch)),
      (request.nationalId.exists(_.onlyDigits.nonEmpty), hasMatch(PiiAttribute.SSNMatch)),
      (request.mobileNumber.exists(_.value.nonEmpty), hasMatch(PiiAttribute.MobileNumberMatch)),
      (request.email.exists(_.nonEmpty), hasMatch(PiiAttribute.EmailMatch)),
      (isAddressProvided, hasMatch(PiiAttribute.StreetAddressMatch) && (hasMatch(PiiAttribute.ZipCodeMatch) || (hasMatch(PiiAttribute.CityMatch) && hasMatch(PiiAttribute.StateMatch))))
    )

    val inputCount = piiMatches.count(_._1)
    val matchCount = piiMatches.count(_._2)
    if (inputCount == matchCount) true
    else if (inputCount == 1 && matchCount == 1) true
    else if (inputCount == 2 && matchCount == 2) true
    else if (inputCount == 3 && matchCount >= 2) true
    else if (inputCount == 4 && matchCount >= 3) true
    else if (inputCount == 5 && matchCount >= 3) true
    else if (inputCount == 6 && matchCount >= 4) true
    else false
  }
}