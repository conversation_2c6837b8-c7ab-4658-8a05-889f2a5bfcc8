package me.socure.kycsearch.rest.utils

import me.socure.common.kyc.model.PiiAttribute._
import me.socure.common.kyc.model._
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{FlatSpec, Matchers}

class BestMatchEntityPresenterUtilsTest extends FlatSpec with Matchers with MockitoSugar {

  import BestMatchEntityPresenterUtils._

  "validatePrefillStatus" should "return true when all input matched" in {
    val req = buildRequest(firstName = true, surName = true, dob = true, ssn = true, email = true, mobile = true, address = true)
    val matches = Map(FirstNameMatch -> 1, SurNameMatch -> 1, DOBMatch -> 1, SSNMatch -> 1, StreetAddressMatch -> 1, CityMatch -> 1, ZipCodeMatch -> 1, StateMatch -> 1, MobileNumberMatch -> 1, EmailMatch -> 1)
    validatePrefillStatus(req, matches) shouldBe true
  }

  it should "return true for 1 input and 1 match" in {
    val req = buildRequest(firstName = true, surName = true)
    val matches = Map(FirstNameMatch -> 1, SurNameMatch -> 1)
    validatePrefillStatus(req, matches) shouldBe true
  }

  it should "return true for 2 inputs and 2 matches" in {
    val req = buildRequest(firstName = true, surName = true, dob = true)
    val matches = Map(FirstNameMatch -> 1, SurNameMatch -> 1, DOBMatch -> 1)
    validatePrefillStatus(req, matches) shouldBe true
  }

  it should "return true for 3 inputs and 2 matches" in {
    val req = buildRequest(firstName = true, surName = true, dob = true, ssn = true)
    val matches = Map(FirstNameMatch -> 1, SurNameMatch -> 1, DOBMatch -> 1)
    validatePrefillStatus(req, matches) shouldBe true
  }

  it should "return true for 4 inputs and 3 matches" in {
    val req = buildRequest(firstName = true, surName = true, dob = true, ssn = true, email = true)
    val matches = Map(FirstNameMatch -> 1, SurNameMatch -> 1, DOBMatch -> 1, SSNMatch -> 1)
    validatePrefillStatus(req, matches) shouldBe true
  }

  it should "return true for 5 inputs and 3 matches" in {
    val req = buildRequest(firstName = true, surName = true, dob = true, ssn = true, email = true, mobile = true)
    val matches = Map(FirstNameMatch -> 1, SurNameMatch -> 1, DOBMatch -> 1, EmailMatch -> 1)
    validatePrefillStatus(req, matches) shouldBe true
  }

  it should "return true for 6 inputs and 4 matches" in {
    val req = buildRequest(firstName = true, surName = true, dob = true, ssn = true, email = true, mobile = true, address = true)
    val matches = Map(
      FirstNameMatch -> 1, SurNameMatch -> 1, DOBMatch -> 1, EmailMatch -> 1,
      StreetAddressMatch -> 1, ZipCodeMatch -> 1
    )
    validatePrefillStatus(req, matches) shouldBe true
  }

  it should "return false for 2 inputs and 1 match" in {
    val req = buildRequest(firstName = true, surName = true, dob = true)
    val matches = Map(FirstNameMatch -> 1) // SurName or DOB not matched
    validatePrefillStatus(req, matches) shouldBe false
  }

  it should "return false for 3 inputs and 1 match" in {
    val req = buildRequest(firstName = true, surName = true, dob = true, ssn = true)
    val matches = Map(FirstNameMatch -> 1)
    validatePrefillStatus(req, matches) shouldBe false
  }

  it should "return false for 4 inputs and 2 matches" in {
    val req = buildRequest(firstName = true, surName = true, dob = true, ssn = true, email = true)
    val matches = Map(FirstNameMatch -> 1, DOBMatch -> 1)
    validatePrefillStatus(req, matches) shouldBe false
  }

  it should "return false for 5 inputs and 2 matches" in {
    val req = buildRequest(firstName = true, surName = true, dob = true, ssn = true, email = true, mobile = true)
    val matches = Map(FirstNameMatch -> 1, DOBMatch -> 1)
    validatePrefillStatus(req, matches) shouldBe false
  }

  it should "return false for 6 inputs and 3 matches" in {
    val req = buildRequest(firstName = true, surName = true, dob = true, ssn = true, email = true, mobile = true, address = true)
    val matches = Map(FirstNameMatch -> 1, DOBMatch -> 1, EmailMatch -> 1)
    validatePrefillStatus(req, matches) shouldBe false
  }

  def buildRequest(
                    firstName: Boolean = false,
                    surName: Boolean = false,
                    dob: Boolean = false,
                    ssn: Boolean = false,
                    email: Boolean = false,
                    mobile: Boolean = false,
                    address: Boolean = false
                  ): KycEntitySearchRequest = {
    KycEntitySearchRequest(
      firstName = if (firstName) FirstName("John") else FirstName(""),
      surName = if (surName) SurName("Smith") else SurName(""),
      dob = if (dob) Some(DOB("1997-11-27")) else None,
      nationalId = if (ssn) Some(NationalId("123456789")) else None,
      driverLicense = None,
      email = if (email) Some("<EMAIL>") else None,
      mobileNumber = if (mobile) Some(MobileNumber("**********")) else None,
      streetAddress = if (address) Some(StreetAddress("XYZ street")) else None,
      city = if (address) Some(City("Washington")) else None,
      state = if (address) Some(State("DC")) else None,
      zipCode = if (address) Some(ZipCode("12345")) else None,
      zip4 = None,
      latitude = None,
      longitude = None,
      preferencesKyc = null,
      preferencesEntity = null,
      customPreferencesKyc = null,
      maskPii = false,
      workflows = Set.empty
    )
  }
}
