threadpool {
  poolSize = 50
}

server {
  port = 5000
  apiTimeout = "10 minutes"
}

healthcheck.thresholds {
  memory {
    heap.used_max_percentage = "<95.0" //should be less than 95%
    non_heap.used_max_percentage = "<95.0" //should be less than 95%
  }

  thread {
    deadlock.count = "<=0.0"
    monitor.deadlock.count = "<=0.0"
  }
}

security.hmac {
  secret.key="""ENC(bCRzcBewPEbjyy9D2i3Xxb/yYinyOtWclerCTA7nEaU0f1o02IIyoJ20PneRMwnWFs9so2X410qN)"""
  ttl=5
  time.interval=5
  strength=512
}

cors {
  allowedDomains = [
    "http://swagger.us-east-vpc.socure.be"
  ]
}

jmx {
  port = 1098
}

match.weighting {
  useControlCenter = false
  controlCentreTimeout = 5
  weights {
    default-match-weights {
      firstNameWeight = 4.0,
      lastNameWeight = 9.0,
      ssnWeight = 10.0,
      ssn4Weight = 10.0,
      dobWeight = 6.0,
      phoneWeight = 1.0,
      emailWeight = 1.0,
      streetAddressWeight = 4.0,
      cityWeight = 3.0,
      zipWeight = 2.0,
      stateWeight = 1.0
    }
    challenger-match-weights {
      firstNameWeight = 5.0,
      lastNameWeight = 4.0,
      ssnWeight = 9.0,
      ssn4Weight = 9.0,
      dobWeight = 9.0,
      phoneWeight = 2.0,
      emailWeight = 1.0,
      streetAddressWeight = 3.0,
      cityWeight = 2.0,
      zipWeight = 2.0,
      stateWeight = 7.0
    }
  }
}

### Remove below 2 once fix for above is deployed

default-match-weights {
  firstNameWeight=4.0,
  lastNameWeight=9.0,
  ssnWeight=10.0,
  dobWeight=6.0,
  phoneWeight=4.0,
  streetAddressWeight=4.0,
  cityWeight=3.0,
  zipWeight=2.0,
  stateWeight=1.0
}

challenger-match-weights {
  firstNameWeight=5.0,
  lastNameWeight=4.0,
  ssnWeight=9.0,
  dobWeight=9.0,
  phoneWeight=1.0,
  streetAddressWeight=3.0,
  cityWeight=2.0,
  zipWeight=2.0,
  stateWeight=7.0
}

obitscrapper.elastic {
  enabled = false
  host =  "vpc-kyc-scrapped-pii-prod-hpo3pqzun2a5wyvsiznnasxg5a.us-east-1.es.amazonaws.com"
  port = 443
  protocol = "https"
  region = "us-east-1"
  indexName = "identities-cid-deceased-data"
  querySize = 100
  timeoutInMillis = 100
  dynamo {
    enabled = true
    table = "kyc_identity_data_deceased_202409_unified"
  }
}

######################

elastic {
  host="aos-425605743478-6dwg5sint35np34ipiwflh2txq.us-gov-west-1.es.amazonaws.com"
  port=443
  protocol="https"
  region="us-gov-west-1"
  indexName="identities-cid-stage-new"
  querySize=100
  dynamo {
    enabled = true
    table = "kyc_identity_data_enf_stage_unified"
  }
}

enf.elastic {
  host="aos-425605743478-6dwg5sint35np34ipiwflh2txq.us-gov-west-1.es.amazonaws.com"
  port=443
  protocol="https"
  region="us-gov-west-1"
  indexName="identities-cid-stage-new"
  querySize=100
  dynamo {
    enabled = true
    table = "kyc_identity_data_enf_stage_unified"
  }
}

obitscrapper.elastic {
  enabled = false
  host =  "aos-fc835eee5ecd-upvsrdltm2opndodpkzxplyj7q.us-gov-west-1.es.amazonaws.com"
  port = 443
  protocol = "https"
  region = "us-east-1"
  indexName = "identities-cid-deceased-data"
  querySize = 10
  dynamo {
    enabled = true
    table = "kyc_identity_data_dev"
  }
}

docv.datasource {
  elastic {
    enabled = false
    host = "aos-893487f7f921-xwtdz2ceyxt74jezwuvnfufzhe.us-gov-west-1.es.amazonaws.com"
    port = 443
    protocol = "https"
    region="us-gov-west-1"
    indexName = "identities-cid-docv-new"
    querySize = 100
    timeoutInMillis = 150
  }
}

ssn.dynamodb {
  table = "kyc_identity_data_202502_ssn_tbl_unified"
  correctionlog = {
    enabled = true
    table = "kyc_ssn_corrections_tbl_stage"
  }
}

#===================Control Center==========================#

control.center {
  s3 {
    bucketName = "idplus-global-settings-************-us-gov-west-1"
  }
  memcached {
    host="memcached-4648b7.rrznfx.cfg.usgw1.cache.amazonaws.com"
    port=11211
    ttl="24 hours"
  }
  local.cache {
    timeout.minutes=2
  }
}

#===================Control Center==========================#

enformiom.merge.suppressed {
  accountIds = []
}

#==================== SQS for Display BME in Admin Dashboard ==========================#

sqs {
  primary {
    region = "us-gov-west-1",
    queue = "transaction-resolved-entity-worker-stage-b6304ede"
  }
  secondary {
    region = "us-gov-west-1",
    queue = "transaction-resolved-entity-worker-stage-b6304ede"
  }
  s3.fallback {
    region = "us-gov-west-1",
    bucket = "transaction-resolved-entity-stage-************-us-gov-west-1",
    basePath = "BestMatchEntity",
    kmsId = "arn:aws-us-gov:kms:us-gov-west-1:************:key/972b4f9e-b56c-4ea7-a412-5cf234e1629e"
  }
  retry {
    initial.backoff = "1 seconds",
    max.backoff = "32 seconds",
    multiplier = 2,
    max.attempts = 5
  }
}

unified.entity {
  elastic {
    enabled = false
    host="vpc-kyc-search-prod-unified-blue-rzhfle5e6vmdnmwuo4rv6hqu4i.us-gov-west-1.es.amazonaws.com"
    port=443
    protocol="https"
    region="us-gov-west-1"
    indexName="identities-cid"
    querySize=10
  }
  sqs {
    messagePush = false
    primary {
      region = "us-gov-west-1",
      queue = "transaction-resolved-entity-worker-stage-b6304ede"
    }
    secondary {
      region = "us-gov-west-1",
      queue = "transaction-resolved-entity-worker-stage-b6304ede"
    }
    s3.fallback {
      region = "us-gov-west-1",
      bucket = "transaction-resolved-entity-stage-************-us-gov-west-1",
      basePath = "BestMatchEntity",
      kmsId = "arn:aws-us-gov:kms:us-gov-west-1:************:key/972b4f9e-b56c-4ea7-a412-5cf234e1629e"
    }
    retry {
      initial.backoff = "1 seconds",
      max.backoff = "32 seconds",
      multiplier = 2,
      max.attempts = 5
    }
  }
}

memcached {
  host=memcached-4648b7.rrznfx.cfg.usgw1.cache.amazonaws.com
  port=11211
}

#===================Account Service===================#
account.service {
  endpoint="http://account-service"
  endpoint2="http://account-service"
  groupName="UXServicesRamp"
  flagName="AccountService_Ramp"
  region = "us-gov-west-1"
  hmac {
    secret.key="notused"
    strength=512
    realm="Socure"
    version = "1.0"
  }
}
#===================Account Service===================#

ecbsv.inhouse {
  enabled = false
  timeout = 200
  db = {
    fetch.limit = 10
    useLocalDB = false
    retry {
      interval = "10 milliseconds"
      attempts = 2
    }
  }
}

dynamic.control.center {
  s3 {
    bucketName = "globalconfig-************-us-gov-west-1"
  }
  memcached {
    host=memcached-4648b7.rrznfx.cfg.usgw1.cache.amazonaws.com
    port=11211
    ttl=86400
  }
  local {
    cache.timeout.minutes=2
    cache.read.timeout.milliseconds = 600
  }
}

transaction-auditing {
  threadpool {
    poolSize=30
  }

  aws {
    maxRetries = 10

    primary {
      sqs {
        region=us-gov-west-1
        transaction {
          queueName=transaction-auditing-stage
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        third-party {
          queueName=third-party-transaction-auditing-stage
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        producer {
          #maximum number of messages being processed by AmazonSQSAsync at the same time
          transaction {
            maxInFlight: 15
          }
          third-party {
            maxInFlight: 15
          }
        }
      }
    }

    fallback0 {
      sqs {
        region=us-gov-west-1
        transaction {
          queueName=transaction-auditing-stage
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        third-party {
          queueName=third-party-transaction-auditing-stage
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        producer {
          #maximum number of messages being processed by AmazonSQSAsync at the same time
          transaction {
            maxInFlight: 15
          }
          third-party {
            maxInFlight: 15
          }
        }
      }
    }

    fallback1 {
      sqs {
        region=us-gov-west-1
        transaction {
          queueName=transaction-auditing-stage
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        third-party {
          queueName=third-party-transaction-auditing-stage
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        producer {
          #maximum number of messages being processed by AmazonSQSAsync at the same time
          transaction {
            maxInFlight: 15
          }
          third-party {
            maxInFlight: 15
          }
        }
      }
    }

    s3 {
      largefiles {
        folder="sqs-storage-stage-************-us-gov-west-1"
      }
      third-party {
        region=us-gov-west-1
        bucket="thirdparty-stats-stage-************-us-gov-west-1"
      }
    }

    sqs {
      backoff {
        # seconds
        min: 2
        max: 32
      }
    }
  }
}

## Comment to disable
## melissa_data {
##     host = "https://globalpersonator.melissadata.net"
##     idv.api = "/v1/doContactVerify"
##     license = "melissa-data/idv/prod/license"
## }

#Set dynamo.enabled= true before enabling dynamo change for anchor.

unique.id {
  sqs.push.enabled = false
  retry {
    interval = "10 milliseconds"
    attempts = 2
  }
  dynamodb {
      tableName = "kyc_entity_unification_stage"
  }
  timeout.generate = "500 milliseconds"
}

anchor.enabled=false
dynamo.enabled=true
dynamo.kyc.config.table_name=kyc_table_config_stage
dynamo {
  scheduler {
    poolsize = 2
    interval.seconds = 120
  }
  region = "us-gov-west-1"
  maxConcurrency = 100
}

mock {
  s3 {
    bucketName = "transaction-resolved-entity-stage-************-us-gov-west-1"
  }
}

server.metrics.enabled = false

internal_entity {
  elastic {
    host = "aos-893487f7f921-xwtdz2ceyxt74jezwuvnfufzhe.us-gov-west-1.es.amazonaws.com"
    port = 443
    protocol = "https"
    region = "us-gov-west-1"
    indexName = "identities-cid-internal-entity"
    querySize = 15
  }
  timeout = 1000
  enabled = true
  dynamo.table = "kyc_internal_entity_stage"
}

deceasedConfigName = "elasticSearchConnectionsConf"

show {
  top20obitRecords = false
  nationalIdQueryResults = false
  additonalMatches = false
  top20EquifaxEntities = false
  top20EnformionEntities = false
}

#================ Smarty Streets ================#

smartystreets.service {
  endpoint = "http://smartystreets"
}

#================ Smarty Streets ================#

rulecode {
  configTable = "rc_config_stage"
  currentConfigKey = "rc_table_config"
  table.refresh.enabled = true
  scheduler {
    poolsize = 2
    interval.seconds = 120
  }
  tower_data_table_name = {
    "table_name" : "towerdata_email_lookup",
    "email_first_seen_path" : "eam.date_first_seen",
    "is_data_compressed" : true
  }
  full_contact_table_name = {
    "table_name" : "fullcontact_email_lookup",
    "email_first_seen_path" : "persons.emailFirstSeen",
    "email_last_seen_path" : "persons.emailLastSeen",
    "is_data_compressed" : true
  }
  production_data_table_name = {
    "table_name" : "sv4_correlation_features",
    "email_first_seen_path" : "fsd",
    "email_last_seen_path" : "lsd",
    "is_data_compressed" : false
  }
}

email.processing.enabled = false

attom {
    enabled = true
    timeout = "500 milliseconds"
}