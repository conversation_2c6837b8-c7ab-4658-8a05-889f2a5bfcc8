package me.socure.search.client

import com.fasterxml.jackson.databind.ObjectMapper
import com.sksamuel.elastic4s.ElasticDsl._
import com.sksamuel.elastic4s.requests.analyzers.{Analyzer, CustomAnalyzerDefinition, StandardTokenizer}
import com.sksamuel.elastic4s.requests.count.CountResponse
import com.sksamuel.elastic4s.requests.indexes.{CreateIndexRequest, CreateIndexResponse}
import com.sksamuel.elastic4s.requests.mappings.dynamictemplate.DynamicMapping
import com.sksamuel.elastic4s.requests.searches.SearchResponse
import com.sksamuel.elastic4s.requests.searches.queries.Query
import com.sksamuel.elastic4s.{ElasticClient, RequestFailure, RequestSuccess, Response, XContentBuilder, XContentFactory}
import me.socure.common.kyc.factory.QueryParserFactory
import scala.concurrent.duration._

import javax.inject.{Inject, Named}
import me.socure.common.kyc.model.QuerySize
import me.socure.common.kyc.model.es.result.{Elastic4sResult, Records}
import me.socure.common.kyc.model.es.result.Elastic4sResultExtensions._
import me.socure.common.kyc.service.IdentityDataDynamoService
import me.socure.common.kyc.util.FutureUtils
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import org.slf4j.{Logger, LoggerFactory}

import javax.annotation.Nullable
import scala.concurrent.{ExecutionContext, Future}

class LegacyKycElasticSearchClient @Inject() (client: ElasticClient,
                                              @Named("querySize") querySize: QuerySize,
                                              @Named("useEfxDynamoSource") useDynamoSource: Boolean,
                                              @Nullable @Named("efxDynamoService") dynamoService: IdentityDataDynamoService,
                                              vendor: String = "equifax",
                                              timeout: Int = -1) extends SearchClient {
  implicit val formats = org.json4s.DefaultFormats

  private val logger: Logger = LoggerFactory.getLogger(getClass)
  private val metrics: Metrics = JavaMetricsFactory.get(classOf[LegacyKycElasticSearchClient])
  private val dynamoDbMetrics: Metrics = JavaMetricsFactory.get("dynamodb.identity")

  def shutDown(): Unit = {
    client.close()
  }

  override def execute(inputQuery: Query, index: String)(implicit ec: ExecutionContext): Future[Elastic4sResult] = {

    if(timeout != -1){
      Future.firstCompletedOf(Seq(executeHybridQuery(inputQuery, index).map(Right(_)), FutureUtils.delay(timeout.milliseconds) {
        (s"Time limit exceeded for ES Search for vendor: $vendor")
      }.map(Left(_))))
      .map {
        case Left(resultStr) =>
          logger.error(s"$resultStr")
          metrics.increment("search.query.timed.out", s"vendor:$vendor")
          Elastic4sResult(Array.empty)
        case Right(result) => result
      }
    }
    else {
      executeHybridQuery(inputQuery, index)
    }

  }

  private def executeHybridQuery(inputQuery: Query, index: String)(implicit ec: ExecutionContext) = {
    client.execute {
      search(index) query inputQuery size querySize.value
    }.flatMap {
      case failure: RequestFailure =>
        logger.error(s"Failed to execute elasticsearch request : $failure")
        throw new Exception("Failed to execute elasticsearch request: " + failure.error)
      case results: RequestSuccess[SearchResponse] if !results.isError =>
          if (useDynamoSource) {
            val rawResultsFuture = dynamoDbMetrics.time("fetch", "service:kyc", s"vendor:$vendor")(
              dynamoService.fetchSourceDataAsync(results.result.hits.hits.map(_.id).distinct)
            )
            rawResultsFuture.map { rawResults =>
              (QueryParserFactory.parseIdentityResults(rawResults), QueryParserFactory.parseIdentityResultsV2(rawResults))
            }
          } else {
            Future.successful((QueryParserFactory.parseQueryIdentityResultsV1(results), QueryParserFactory.parseIdentityResultsFromESResponseV2(results)))
          }
      case res =>
        logger.error(s"Unknown result $res")
        throw new Exception("Exceptional result:" + res)
    }.map { case (parsedResults, parsedIdentityRecords) =>
      logger.debug(s"ElasticSearch results size: ${parsedResults.length}")
      val elastic4sResult = Elastic4sResult(parsedResults, Some(parsedIdentityRecords))
      elastic4sResult
    }
  }

  def countIndex(index: String): Future[Response[CountResponse]] = {
    client.execute {
      count(index)
    }
  }
}
